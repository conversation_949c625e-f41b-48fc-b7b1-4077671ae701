cmake_minimum_required(VERSION 3.21 FATAL_ERROR)

project(GeodeBindings VERSION 1.0.0)

if (NOT DEFINED GEODE_TARGET_PLATFORM)
	message(FATAL_ERROR "GEODE_TARGET_PLATFORM is not defined.")
endif()
if (NOT DEFINED GEODE_GD_VERSION)
	message(FATAL_ERROR "GEODE_GD_VERSION is not defined.")
endif()
if (NOT DEFINED GEODE_LOADER_PATH)
	message(FATAL_ERROR "GEODE_LOADER_PATH is not defined.")
endif()

option(
	BINDINGS_VERSIONED_ONLY
	"Disables inclusion of the unversioned Enums header, allowing for builds on older Geode/Geometry Dash versions."
	OFF
)

if (NOT GEODE_BINDINGS_PATH)
	set(GEODE_BINDINGS_PATH ${CMAKE_CURRENT_SOURCE_DIR}/bindings/${GEODE_GD_VERSION})
endif()

set(GEODE_CODEGEN_BINARY_OUT ${CMAKE_CURRENT_BINARY_DIR}/codegen)
set(GEODE_CODEGEN_PATH ${CMAKE_CURRENT_BINARY_DIR}/bindings)

set(GEODE_CODEGEN_SOURCE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/codegen)
set(GEODE_CODEGEN_BINARY_DIR ${CMAKE_CURRENT_BINARY_DIR}/codegen-build)

if (NOT DEFINED SKIP_BUILDING_CODEGEN)
	if (CMAKE_HOST_SYSTEM_NAME STREQUAL "Windows")
		set(GEODE_CODEGEN_DOWNLOAD_FILE_NAME geode-codegen-win.exe)
		set(GEODE_CODEGEN_TARGET_FILE_NAME Codegen.exe)
	elseif (CMAKE_HOST_SYSTEM_NAME STREQUAL "Linux")
		set(GEODE_CODEGEN_DOWNLOAD_FILE_NAME geode-codegen-linux)
		set(GEODE_CODEGEN_TARGET_FILE_NAME Codegen)
	elseif (CMAKE_HOST_SYSTEM_NAME STREQUAL "Darwin")
		set(GEODE_CODEGEN_DOWNLOAD_FILE_NAME geode-codegen-mac)
		set(GEODE_CODEGEN_TARGET_FILE_NAME Codegen)
	else()
		unset(GEODE_CODEGEN_DOWNLOAD_FILE_NAME)
	endif()
	if (DEFINED GEODE_CODEGEN_DOWNLOAD_FILE_NAME)
		message(STATUS "Downloading Codegen")
		message(NOTICE "Set SKIP_BUILDING_CODEGEN to OFF to force codegen to build locally.")
		# have to run file(DOWNLOAD) twice to check if it exists first because
		# cmake is silly and fails the entire configure even if i have STATUS set
		file(DOWNLOAD
			https://github.com/geode-sdk/bindings/releases/download/codegen/${GEODE_CODEGEN_DOWNLOAD_FILE_NAME}
			STATUS GEODE_CODEGEN_DOWNLOAD_STATUS
		)
		list(GET GEODE_CODEGEN_DOWNLOAD_STATUS 0 GEODE_CODEGEN_DOWNLOAD_STATUS_CODE)
		if (${GEODE_CODEGEN_DOWNLOAD_STATUS_CODE} EQUAL 0)
			file(MAKE_DIRECTORY ${GEODE_CODEGEN_BINARY_OUT})
			file(DOWNLOAD
				https://github.com/geode-sdk/bindings/releases/download/codegen/${GEODE_CODEGEN_DOWNLOAD_FILE_NAME}
				${GEODE_CODEGEN_BINARY_OUT}/${GEODE_CODEGEN_TARGET_FILE_NAME}
				STATUS GEODE_CODEGEN_DOWNLOAD_STATUS
			)
			list(GET GEODE_CODEGEN_DOWNLOAD_STATUS 0 GEODE_CODEGEN_DOWNLOAD_STATUS_CODE)
		endif()
		if (${GEODE_CODEGEN_DOWNLOAD_STATUS_CODE} EQUAL 0)
			file(
				CHMOD ${GEODE_CODEGEN_BINARY_OUT}/${GEODE_CODEGEN_TARGET_FILE_NAME}
				PERMISSIONS
					OWNER_READ OWNER_WRITE OWNER_EXECUTE
					GROUP_READ GROUP_WRITE GROUP_EXECUTE
					WORLD_READ WORLD_WRITE WORLD_EXECUTE
			)
			message(STATUS "Downloading Codegen - success")
			set(SKIP_BUILDING_CODEGEN ON)
		else()
			list(GET GEODE_CODEGEN_DOWNLOAD_STATUS 1 GEODE_CODEGEN_DOWNLOAD_STATUS_MESSAGE)
			message(STATUS "Downloading Codegen - fail: ${GEODE_CODEGEN_DOWNLOAD_STATUS_MESSAGE}")
			set(SKIP_BUILDING_CODEGEN OFF)
		endif()
	else()
		set(SKIP_BUILDING_CODEGEN OFF)
	endif()
	unset(GEODE_CODEGEN_DOWNLOAD_FILE_NAME)
	unset(GEODE_CODEGEN_TARGET_FILE_NAME)
endif()

if (NOT SKIP_BUILDING_CODEGEN)
	file(MAKE_DIRECTORY ${GEODE_CODEGEN_BINARY_DIR})

	if (DEFINED CROSS_TOOLCHAIN_FLAGS_NATIVE)
		message(STATUS "Passing CROSS_TOOLCHAIN_FLAGS_NATIVE to Codegen")
		set(GEODE_CODEGEN_CROSS_TOOLCHAIN_FLAGS_NATIVE -DCROSS_TOOLCHAIN_FLAGS_NATIVE=${CROSS_TOOLCHAIN_FLAGS_NATIVE})
	endif()

	set(PREV_SCCACHE_C_CUSTOM_CACHE_BUSTER ENV{SCCACHE_C_CUSTOM_CACHE_BUSTER})
	set(ENV{SCCACHE_C_CUSTOM_CACHE_BUSTER} "codegen")

	message(STATUS "Configuring Codegen")
	execute_process(
		COMMAND ${CMAKE_COMMAND} ${GEODE_CODEGEN_CMAKE_ARGS}
			-DCMAKE_C_COMPILER_LAUNCHER=${CMAKE_C_COMPILER_LAUNCHER}
			-DCMAKE_CXX_COMPILER_LAUNCHER=${CMAKE_CXX_COMPILER_LAUNCHER}
			${GEODE_CODEGEN_CROSS_TOOLCHAIN_FLAGS_NATIVE}
			-DCMAKE_BUILD_TYPE=Release
			-DCMAKE_INSTALL_PREFIX:STRING=${GEODE_CODEGEN_BINARY_OUT}
			-S ${GEODE_CODEGEN_SOURCE_DIR} -B ${GEODE_CODEGEN_BINARY_DIR}
		WORKING_DIRECTORY ${GEODE_CODEGEN_SOURCE_DIR}
		COMMAND_ECHO STDOUT
		COMMAND_ERROR_IS_FATAL ANY
	)
	message(STATUS "Building Codegen")
	execute_process(
		COMMAND ${CMAKE_COMMAND} --build ${GEODE_CODEGEN_BINARY_DIR} --config Release
		WORKING_DIRECTORY ${GEODE_CODEGEN_SOURCE_DIR}
		COMMAND_ECHO STDOUT
		COMMAND_ERROR_IS_FATAL ANY
	)
	execute_process(
		COMMAND ${CMAKE_COMMAND} --install ${GEODE_CODEGEN_BINARY_DIR} --config Release
			--prefix ${GEODE_CODEGEN_BINARY_OUT}
		WORKING_DIRECTORY ${GEODE_CODEGEN_SOURCE_DIR}
		COMMAND_ECHO STDOUT
		COMMAND_ERROR_IS_FATAL ANY
	)

	set(ENV{SCCACHE_C_CUSTOM_CACHE_BUSTER} PREV_SCCACHE_C_CUSTOM_CACHE_BUSTER)
else()
	message(STATUS "Skipping building Codegen")
	message(VERBOSE "Codegen CMake args were: '${GEODE_CODEGEN_CMAKE_ARGS}'")
endif()

message(STATUS "Running Codegen")
execute_process(
	COMMAND ./Codegen ${GEODE_TARGET_PLATFORM} ${GEODE_BINDINGS_PATH} ${GEODE_CODEGEN_PATH} ${GEODE_CODEGEN_EXTRA_ARGS}
	WORKING_DIRECTORY ${GEODE_CODEGEN_BINARY_OUT}
	COMMAND_ECHO STDOUT
	COMMAND_ERROR_IS_FATAL ANY
)

file(GLOB GEODE_BINDINGS_FILES ${GEODE_BINDINGS_PATH}/*.bro)
file(GLOB GEODE_CODEGEN_OUT_FILES
	${GEODE_CODEGEN_PATH}/Geode/CodegenData.txt
	${GEODE_CODEGEN_PATH}/Geode/*.cpp
	${GEODE_CODEGEN_PATH}/Geode/*.hpp
	${GEODE_CODEGEN_PATH}/Geode/binding/*.hpp
	${GEODE_CODEGEN_PATH}/Geode/modify/*.hpp
)
add_custom_command(
	DEPENDS ${GEODE_BINDINGS_FILES}
	COMMENT "Running Codegen"
	COMMAND ./Codegen ${GEODE_TARGET_PLATFORM} ${GEODE_BINDINGS_PATH} ${GEODE_CODEGEN_PATH} ${GEODE_CODEGEN_EXTRA_ARGS}
	WORKING_DIRECTORY ${GEODE_CODEGEN_BINARY_OUT}
	OUTPUT ${GEODE_CODEGEN_OUT_FILES}
)

if (GEODE_IS_MEMBER_TEST)
	set(GEODE_MEMBER_TEST_CODEGEN_PATH ${GEODE_CODEGEN_PATH} PARENT_SCOPE)
	return()
endif()

add_library(${PROJECT_NAME} ${GEODE_CODEGEN_PATH}/Geode/GeneratedSource.cpp)
target_include_directories(${PROJECT_NAME} PUBLIC
	${GEODE_CODEGEN_PATH}
)

if (NOT BINDINGS_VERSIONED_ONLY)
	target_include_directories(${PROJECT_NAME} PUBLIC
		${CMAKE_CURRENT_SOURCE_DIR}/bindings/include
	)
endif()

set_target_properties(${PROJECT_NAME} PROPERTIES CXX_VISIBILITY_PRESET hidden)
target_compile_features(${PROJECT_NAME} PUBLIC cxx_std_20)

if (WIN32)
	if (MSVC)
		target_compile_options(${PROJECT_NAME} PUBLIC /bigobj)
	endif()
elseif (APPLE)
	target_compile_options(${PROJECT_NAME} PUBLIC -ffunction-sections -fdata-sections)
	target_link_options(${PROJECT_NAME} PUBLIC -dead_strip)
	if ("${CMAKE_SYSTEM_NAME}" STREQUAL "iOS" OR IOS)
		add_definitions(-DGLES_SILENCE_DEPRECATION)
	endif()
elseif (ANDROID)
	target_compile_options(${PROJECT_NAME} PUBLIC -ffunction-sections -fdata-sections)
	target_link_options(${PROJECT_NAME} PUBLIC -Wl,--gc-sections)
endif()
