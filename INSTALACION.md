# 🐷 cerdoOINK MOD - Instalación Completa

## 🎉 ¡Mod Compilado Exitosamente!

El **cerdoOINK MOD** se ha compilado correctamente y está listo para usar.

## 📁 Archivo del Mod

**Ubicación**: `build/cerdooink.staticcube.geode`

Este es el archivo que necesitas instalar en Geometry Dash.

## 🚀 Instalación Paso a Paso

### **1. Localizar el Archivo**
- Ve a la carpeta: `cerdoOINK-MOD/build/`
- Busca el archivo: `cerdooink.staticcube.geode`

### **2. Copiar a Geode**
Copia el archivo a tu carpeta de mods de Geode:

**Windows:**
```
C:\Users\<USER>\AppData\Local\GeometryDash\geode\mods\
```

**Pasos detallados:**
1. Presiona `Win + R`
2. Escribe: `%localappdata%\GeometryDash\geode\mods`
3. Presiona Enter
4. Pega el archivo `cerdooink.staticcube.geode`

### **3. Reiniciar Geometry Dash**
- Cierra Geometry Dash completamente
- Vuelve a abrirlo
- El mod se cargará automáticamente

## 🎮 Cómo Usar

### **Activar Cubo Estático:**
1. **Entra a cualquier nivel**
2. **Presiona la tecla Z**
3. **Verás**: "Cubo Estático: ON"
4. **¡El cubo ya no rotará!**

### **Desactivar:**
1. **Presiona Z nuevamente**
2. **Verás**: "Cubo Estático: OFF"
3. **El cubo volverá a rotar normalmente**

## ✅ Verificar Instalación

### **En el Menú de Geode:**
1. Abre Geometry Dash
2. Ve al menú principal
3. Haz clic en el botón de **Geode**
4. Busca **"cerdoOINK MOD"** en la lista
5. Debe aparecer como **habilitado** ✅

### **En el Juego:**
1. Entra a cualquier nivel
2. Presiona **Z**
3. Debe aparecer la notificación en pantalla

## 🔧 Recompilar (Opcional)

Si quieres modificar el mod y recompilarlo:

```bash
geode build
```

El nuevo archivo se generará en `build/cerdooink.staticcube.geode`

## 🐛 Solución de Problemas

### **"El mod no aparece en Geode"**
- Verifica que el archivo esté en la carpeta correcta
- Asegúrate de que el archivo se llame exactamente `cerdooink.staticcube.geode`
- Reinicia Geometry Dash completamente

### **"La tecla Z no funciona"**
- Asegúrate de estar **dentro de un nivel** (no en menús)
- Verifica que el mod esté **habilitado** en Geode
- Prueba en diferentes niveles

### **"No veo las notificaciones"**
- Las notificaciones aparecen en el centro de la pantalla
- Se desvanecen después de 2 segundos
- Si no las ves, el mod sigue funcionando

## 🎯 Características del Mod

- **✅ Ligero**: Solo 1 archivo, sin dependencias
- **✅ Simple**: Una sola tecla (Z) para todo
- **✅ Compatible**: Funciona con otros mods
- **✅ Estable**: No afecta el rendimiento del juego
- **✅ Visual**: Notificaciones claras del estado

## 🎉 ¡Disfruta!

Ahora tienes el **cerdoOINK MOD** funcionando perfectamente. 

**Presiona Z en cualquier nivel para activar el cubo estático y mejorar tu precisión!**

---

**¡Gracias por usar cerdoOINK MOD!** 🐷

*Mod creado independientemente con funcionalidad de cubo estático.*
