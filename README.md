# cerdoOINK MOD

Un mod simple y efectivo para Geometry Dash que permite hacer el cubo estático (sin rotación).

## 🎮 Funcionalidades

### **Cubo Estático**
- **Tecla Z**: Activa/desactiva la rotación del cubo
- **Notificación visual**: Muestra el estado actual en pantalla
- **Funcionamiento simple**: Solo presiona Z y el cubo dejará de rotar

## 🚀 Instalación

1. **Descarga el archivo compilado**: `cerdooink.staticcube.geode`
2. **Copia el archivo** a la carpeta de mods de Geode:
   ```
   C:\Users\<USER>\AppData\Local\GeometryDash\geode\mods\
   ```
3. **Reinicia Geometry Dash**
4. **¡Listo!** El mod estará activo

## 🎯 Cómo Usar

1. **Abre Geometry Dash** y entra a cualquier nivel
2. **Presiona la tecla Z** para activar el cubo estático
3. **Verás una notificación** que dice "Cubo Estático: ON"
4. **El cubo ya no rotará** mientras juegas
5. **Presiona Z nuevamente** para desactivar y volver a la rotación normal

## 🔧 Compilación (Opcional)

Si quieres compilar el mod tú mismo:

### Requisitos:
- Geode SDK instalado
- CMake 3.21 o superior
- Visual Studio 2022 (Windows)

### Pasos:
```bash
mkdir build
cmake -B build -A x64
cmake --build build --config RelWithDebInfo
```

El archivo compilado estará en `build/cerdooink.staticcube.geode`

## 📝 Características Técnicas

- **Ligero**: Solo modifica la rotación del PlayerObject
- **Eficiente**: No afecta el rendimiento del juego
- **Compatible**: Funciona con otros mods de Geode
- **Simple**: Una sola tecla para controlar todo

## 🎨 Detalles del Mod

- **ID**: `cerdooink.staticcube`
- **Versión**: v1.0.0
- **Desarrollador**: cerdoOINK
- **Compatible con**: Geometry Dash 2.206

## 🐛 Solución de Problemas

### El mod no funciona:
1. Verifica que Geode esté instalado correctamente
2. Asegúrate de que el archivo `.geode` esté en la carpeta correcta
3. Reinicia Geometry Dash completamente

### La tecla Z no responde:
1. Verifica que estés en un nivel (no en menús)
2. Asegúrate de que no haya otros mods interceptando la tecla Z
3. Prueba en diferentes niveles

## 🎉 ¡Disfruta!

Ahora puedes jugar con el cubo estático y mejorar tu precisión en niveles difíciles. ¡Que tengas buenos runs!

---

**Creado por cerdoOINK** 🐷
