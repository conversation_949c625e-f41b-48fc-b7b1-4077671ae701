class GJPointDouble {
    double x;
    double y;
}

class EventTriggerInstance {

}

class SongChannelState {
    
}

class SongTriggerState {

}

class SFXTriggerState {

}

class DynamicScrollDelegate {

}

class FMODSound {
    
}

class AdvancedFollowInstance {
    
}

class SFXTriggerInstance {

} 

class ChanceObject {

}

class GameObjectPhysics {

}

class DynamicObjectAction {

}

class GJTransformState {
	float m_scaleX;
	float m_scaleY;
	float m_angleX;
	float m_angleY;
	float m_skewX;
	float m_skewY;
	float m_unk1;
	float m_unk2;
	float m_unk3;
	float m_unk4;
	cocos2d::CCPoint m_unk5;
	cocos2d::CCPoint m_unk6;
	cocos2d::CCPoint m_unk7;
	float m_unk8;
	float m_unk9;
}

class FMODQueuedMusic {

}