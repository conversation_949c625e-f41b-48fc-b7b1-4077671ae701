^C:\USERS\<USER>\DOWNLOADS\CERDOOINK-MOD\BUILD\_DEPS\RESULT-SUBBUILD\CMAKEFILES\FBBC1061137CE7B5D97D7D7742528E4A\RESULT-POPULATE-MKDIR.RULE
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -Dcfgdir=/Debug -P C:/Users/<USER>/Downloads/cerdoOINK-MOD/build/_deps/result-subbuild/result-populate-prefix/tmp/result-populate-mkdirs.cmake
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch C:/Users/<USER>/Downloads/cerdoOINK-MOD/build/_deps/result-subbuild/result-populate-prefix/src/result-populate-stamp/Debug/result-populate-mkdir
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\DOWNLOADS\CERDOOINK-MOD\BUILD\_DEPS\RESULT-SUBBUILD\CMAKEFILES\FBBC1061137CE7B5D97D7D7742528E4A\RESULT-POPULATE-DOWNLOAD.RULE
setlocal
cd C:\Users\<USER>\Downloads\cerdoOINK-MOD\build\_deps
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_MESSAGE_LOG_LEVEL=VERBOSE -P C:/Users/<USER>/Downloads/cerdoOINK-MOD/build/_deps/result-subbuild/result-populate-prefix/tmp/result-populate-gitclone.cmake
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch C:/Users/<USER>/Downloads/cerdoOINK-MOD/build/_deps/result-subbuild/result-populate-prefix/src/result-populate-stamp/Debug/result-populate-download
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\DOWNLOADS\CERDOOINK-MOD\BUILD\_DEPS\RESULT-SUBBUILD\CMAKEFILES\FBBC1061137CE7B5D97D7D7742528E4A\RESULT-POPULATE-UPDATE.RULE
setlocal
cd C:\Users\<USER>\Downloads\cerdoOINK-MOD\build\_deps\result-src
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -Dcan_fetch=YES -DCMAKE_MESSAGE_LOG_LEVEL=VERBOSE -P C:/Users/<USER>/Downloads/cerdoOINK-MOD/build/_deps/result-subbuild/result-populate-prefix/tmp/result-populate-gitupdate.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\DOWNLOADS\CERDOOINK-MOD\BUILD\_DEPS\RESULT-SUBBUILD\CMAKEFILES\FBBC1061137CE7B5D97D7D7742528E4A\RESULT-POPULATE-PATCH.RULE
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch C:/Users/<USER>/Downloads/cerdoOINK-MOD/build/_deps/result-subbuild/result-populate-prefix/src/result-populate-stamp/Debug/result-populate-patch
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\DOWNLOADS\CERDOOINK-MOD\BUILD\_DEPS\RESULT-SUBBUILD\CMAKEFILES\FBBC1061137CE7B5D97D7D7742528E4A\RESULT-POPULATE-CONFIGURE.RULE
setlocal
cd C:\Users\<USER>\Downloads\cerdoOINK-MOD\build\_deps\result-build
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch C:/Users/<USER>/Downloads/cerdoOINK-MOD/build/_deps/result-subbuild/result-populate-prefix/src/result-populate-stamp/Debug/result-populate-configure
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\DOWNLOADS\CERDOOINK-MOD\BUILD\_DEPS\RESULT-SUBBUILD\CMAKEFILES\FBBC1061137CE7B5D97D7D7742528E4A\RESULT-POPULATE-BUILD.RULE
setlocal
cd C:\Users\<USER>\Downloads\cerdoOINK-MOD\build\_deps\result-build
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch C:/Users/<USER>/Downloads/cerdoOINK-MOD/build/_deps/result-subbuild/result-populate-prefix/src/result-populate-stamp/Debug/result-populate-build
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\DOWNLOADS\CERDOOINK-MOD\BUILD\_DEPS\RESULT-SUBBUILD\CMAKEFILES\FBBC1061137CE7B5D97D7D7742528E4A\RESULT-POPULATE-INSTALL.RULE
setlocal
cd C:\Users\<USER>\Downloads\cerdoOINK-MOD\build\_deps\result-build
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch C:/Users/<USER>/Downloads/cerdoOINK-MOD/build/_deps/result-subbuild/result-populate-prefix/src/result-populate-stamp/Debug/result-populate-install
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\DOWNLOADS\CERDOOINK-MOD\BUILD\_DEPS\RESULT-SUBBUILD\CMAKEFILES\FBBC1061137CE7B5D97D7D7742528E4A\RESULT-POPULATE-TEST.RULE
setlocal
cd C:\Users\<USER>\Downloads\cerdoOINK-MOD\build\_deps\result-build
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch C:/Users/<USER>/Downloads/cerdoOINK-MOD/build/_deps/result-subbuild/result-populate-prefix/src/result-populate-stamp/Debug/result-populate-test
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\DOWNLOADS\CERDOOINK-MOD\BUILD\_DEPS\RESULT-SUBBUILD\CMAKEFILES\943F6A9B3C716BA35EA8AAC9D9A4BD41\RESULT-POPULATE-COMPLETE.RULE
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory C:/Users/<USER>/Downloads/cerdoOINK-MOD/build/_deps/result-subbuild/CMakeFiles/Debug
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch C:/Users/<USER>/Downloads/cerdoOINK-MOD/build/_deps/result-subbuild/CMakeFiles/Debug/result-populate-complete
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch C:/Users/<USER>/Downloads/cerdoOINK-MOD/build/_deps/result-subbuild/result-populate-prefix/src/result-populate-stamp/Debug/result-populate-done
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\DOWNLOADS\CERDOOINK-MOD\BUILD\_DEPS\RESULT-SUBBUILD\CMAKEFILES\7DE94929FA33547B41CF40C2B91BC293\RESULT-POPULATE.RULE
setlocal
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\DOWNLOADS\CERDOOINK-MOD\BUILD\_DEPS\RESULT-SUBBUILD\CMAKELISTS.TXT
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/Users/<USER>/Downloads/cerdoOINK-MOD/build/_deps/result-subbuild -BC:/Users/<USER>/Downloads/cerdoOINK-MOD/build/_deps/result-subbuild --check-stamp-file C:/Users/<USER>/Downloads/cerdoOINK-MOD/build/_deps/result-subbuild/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
