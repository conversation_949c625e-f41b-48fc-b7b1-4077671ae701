name: Build and test

on:
  push:
    branches: "**"
  pull_request:
    branches: "**"

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3

    - uses: seanmiddleditch/gha-setup-ninja@v4

    - name: Configure CMake
      run: cmake -B build -G Ninja -DCMAKE_CXX_COMPILER=clang++ -DCMAKE_C_COMPILER=clang

    - name: Build
      run: cmake --build build --parallel

    - name: Test
      working-directory: build/test
      run: ctest
