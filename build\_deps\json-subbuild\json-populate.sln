﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "CMakePredefinedTargets", "CMakePredefinedTargets", "{24BF3C3E-CF77-3D08-9716-8D548EFA24BE}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "ExternalProjectTargets", "ExternalProjectTargets", "{5248A7B7-7EAE-3B2D-BCD2-B118A35E97CA}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "json-populate", "ExternalProjectTargets\json-populate", "{0DCF2BA0-**************-3C3BD06B7BEB}"
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{4213E92C-1EC1-3644-8A95-FD9E5F211766}"
	ProjectSection(ProjectDependencies) = postProject
		{77F0AC7E-8D49-35D9-9AF5-84AE6D0D77AB} = {77F0AC7E-8D49-35D9-9AF5-84AE6D0D77AB}
		{5DF15810-9BCE-334F-87FA-B25EF28E3DDF} = {5DF15810-9BCE-334F-87FA-B25EF28E3DDF}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{77F0AC7E-8D49-35D9-9AF5-84AE6D0D77AB}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "json-populate", "json-populate.vcxproj", "{5DF15810-9BCE-334F-87FA-B25EF28E3DDF}"
	ProjectSection(ProjectDependencies) = postProject
		{77F0AC7E-8D49-35D9-9AF5-84AE6D0D77AB} = {77F0AC7E-8D49-35D9-9AF5-84AE6D0D77AB}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{4213E92C-1EC1-3644-8A95-FD9E5F211766}.Debug|x64.ActiveCfg = Debug|x64
		{77F0AC7E-8D49-35D9-9AF5-84AE6D0D77AB}.Debug|x64.ActiveCfg = Debug|x64
		{77F0AC7E-8D49-35D9-9AF5-84AE6D0D77AB}.Debug|x64.Build.0 = Debug|x64
		{5DF15810-9BCE-334F-87FA-B25EF28E3DDF}.Debug|x64.ActiveCfg = Debug|x64
		{5DF15810-9BCE-334F-87FA-B25EF28E3DDF}.Debug|x64.Build.0 = Debug|x64
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{4213E92C-1EC1-3644-8A95-FD9E5F211766} = {24BF3C3E-CF77-3D08-9716-8D548EFA24BE}
		{77F0AC7E-8D49-35D9-9AF5-84AE6D0D77AB} = {24BF3C3E-CF77-3D08-9716-8D548EFA24BE}
		{0DCF2BA0-**************-3C3BD06B7BEB} = {5248A7B7-7EAE-3B2D-BCD2-B118A35E97CA}
		{5DF15810-9BCE-334F-87FA-B25EF28E3DDF} = {0DCF2BA0-**************-3C3BD06B7BEB}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {D68A63BA-CF91-32D4-B920-ACB1F46763A9}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
