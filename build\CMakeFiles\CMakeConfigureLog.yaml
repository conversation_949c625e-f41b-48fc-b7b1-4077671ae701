
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineSystem.cmake:212 (message)"
      - "CMakeLists.txt:7 (project)"
    message: |
      The system is: Windows - 10.0.22631 - AMD64
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:7 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCCompilerId.c.in"
    candidate_directories:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/"
    found: "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCCompilerId.c.in"
    search_context:
      ENV{PATH}:
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files (x86)\\spwn\\"
        - "C:\\Users\\<USER>\\scoop\\shims"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\GeometryDash"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\GeodeSDK.GeodeCLI_Microsoft.Winget.Source_8wekyb3d8bbwe"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin"
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:7 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      Versión de MSBuild 17.14.14+a129329f1 para .NET Framework
      Compilación iniciada a las 24/7/2025 21:10:02.
      
      Proyecto "C:\\Users\\<USER>\\Downloads\\cerdoOINK-MOD\\build\\CMakeFiles\\4.1.0-rc1\\CompilerIdC\\CompilerIdC.vcxproj" en nodo 1 (destinos predeterminados).
      PrepareForBuild:
        Creando directorio "Debug\\".
        La salida estructurada está habilitada. El formato del diagnóstico del compilador reflejará la jerarquía de errores. Consulte https://aka.ms/cpp/structured-output para obtener más detalles.
        Creando directorio "Debug\\CompilerIdC.tlog\\".
      InitializeBuildStatus:
        Se creará "Debug\\CompilerIdC.tlog\\unsuccessfulbuild" porque se especificó "AlwaysCreate".
        Aplicando tarea Touch a "Debug\\CompilerIdC.tlog\\unsuccessfulbuild".
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TC /FC /errorReport:queue CMakeCCompilerId.c
        CMakeCCompilerId.c
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdC.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdC.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdC.lib" /MACHINE:X64 Debug\\CMakeCCompilerId.obj
        CompilerIdC.vcxproj -> C:\\Users\\<USER>\\Downloads\\cerdoOINK-MOD\\build\\CMakeFiles\\4.1.0-rc1\\CompilerIdC\\CompilerIdC.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_C_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_C_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        Se eliminará el archivo "Debug\\CompilerIdC.tlog\\unsuccessfulbuild".
        Aplicando tarea Touch a "Debug\\CompilerIdC.tlog\\CompilerIdC.lastbuildstate".
      Compilación del proyecto terminada "C:\\Users\\<USER>\\Downloads\\cerdoOINK-MOD\\build\\CMakeFiles\\4.1.0-rc1\\CompilerIdC\\CompilerIdC.vcxproj" (destinos predeterminados).
      
      Compilación correcta.
          0 Advertencia(s)
          0 Errores
      
      Tiempo transcurrido 00:00:00.72
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.exe"
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.vcxproj"
      
      The C compiler identification is MSVC, found in:
        C:/Users/<USER>/Downloads/cerdoOINK-MOD/build/CMakeFiles/4.1.0-rc1/CompilerIdC/CompilerIdC.exe
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:37 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:65 (__resolve_tool_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:103 (__resolve_linker_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:7 (project)"
    mode: "program"
    variable: "_CMAKE_TOOL_WITH_PATH"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "link"
    candidate_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files (x86)/spwn/"
      - "C:/Users/<USER>/scoop/shims/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/AppData/Local/GeometryDash/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/GeodeSDK.GeodeCLI_Microsoft.Winget.Source_8wekyb3d8bbwe/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/Users/<USER>/AppData/Local/Programs/Windsurf/bin/"
    searched_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/link.com"
    found: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/link.exe"
    search_context:
      ENV{PATH}:
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files (x86)\\spwn\\"
        - "C:\\Users\\<USER>\\scoop\\shims"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\GeometryDash"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\GeodeSDK.GeodeCLI_Microsoft.Winget.Source_8wekyb3d8bbwe"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:37 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:65 (__resolve_tool_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:104 (__resolve_linker_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:7 (project)"
    mode: "program"
    variable: "_CMAKE_TOOL_WITH_PATH"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "lld-link"
    candidate_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files (x86)/spwn/"
      - "C:/Users/<USER>/scoop/shims/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/AppData/Local/GeometryDash/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/GeodeSDK.GeodeCLI_Microsoft.Winget.Source_8wekyb3d8bbwe/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/Users/<USER>/AppData/Local/Programs/Windsurf/bin/"
    searched_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/lld-link.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/lld-link.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/lld-link"
      - "C:/Windows/System32/lld-link.com"
      - "C:/Windows/System32/lld-link.exe"
      - "C:/Windows/System32/lld-link"
      - "C:/Windows/lld-link.com"
      - "C:/Windows/lld-link.exe"
      - "C:/Windows/lld-link"
      - "C:/Windows/System32/wbem/lld-link.com"
      - "C:/Windows/System32/wbem/lld-link.exe"
      - "C:/Windows/System32/wbem/lld-link"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link"
      - "C:/Windows/System32/OpenSSH/lld-link.com"
      - "C:/Windows/System32/OpenSSH/lld-link.exe"
      - "C:/Windows/System32/OpenSSH/lld-link"
      - "C:/Program Files/Git/cmd/lld-link.com"
      - "C:/Program Files/Git/cmd/lld-link.exe"
      - "C:/Program Files/Git/cmd/lld-link"
      - "C:/Program Files/CMake/bin/lld-link.com"
      - "C:/Program Files/CMake/bin/lld-link.exe"
      - "C:/Program Files/CMake/bin/lld-link"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/lld-link.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/lld-link.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/lld-link"
      - "C:/Program Files/nodejs/lld-link.com"
      - "C:/Program Files/nodejs/lld-link.exe"
      - "C:/Program Files/nodejs/lld-link"
      - "C:/Program Files (x86)/spwn/lld-link.com"
      - "C:/Program Files (x86)/spwn/lld-link.exe"
      - "C:/Program Files (x86)/spwn/lld-link"
      - "C:/Users/<USER>/scoop/shims/lld-link.com"
      - "C:/Users/<USER>/scoop/shims/lld-link.exe"
      - "C:/Users/<USER>/scoop/shims/lld-link"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/lld-link"
      - "C:/Users/<USER>/AppData/Local/GeometryDash/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/GeometryDash/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/GeometryDash/lld-link"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/GeodeSDK.GeodeCLI_Microsoft.Winget.Source_8wekyb3d8bbwe/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/GeodeSDK.GeodeCLI_Microsoft.Winget.Source_8wekyb3d8bbwe/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/GeodeSDK.GeodeCLI_Microsoft.Winget.Source_8wekyb3d8bbwe/lld-link"
      - "C:/Users/<USER>/AppData/Roaming/npm/lld-link.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/lld-link.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Windsurf/bin/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Windsurf/bin/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Windsurf/bin/lld-link"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files (x86)\\spwn\\"
        - "C:\\Users\\<USER>\\scoop\\shims"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\GeometryDash"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\GeodeSDK.GeodeCLI_Microsoft.Winget.Source_8wekyb3d8bbwe"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:7 (project)"
    mode: "program"
    variable: "CMAKE_LINKER"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "link"
    candidate_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files (x86)/spwn/"
      - "C:/Users/<USER>/scoop/shims/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/AppData/Local/GeometryDash/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/GeodeSDK.GeodeCLI_Microsoft.Winget.Source_8wekyb3d8bbwe/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/Users/<USER>/AppData/Local/Programs/Windsurf/bin/"
    searched_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/link.com"
    found: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/link.exe"
    search_context:
      ENV{PATH}:
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files (x86)\\spwn\\"
        - "C:\\Users\\<USER>\\scoop\\shims"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\GeometryDash"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\GeodeSDK.GeodeCLI_Microsoft.Winget.Source_8wekyb3d8bbwe"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:7 (project)"
    mode: "program"
    variable: "CMAKE_MT"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "mt"
    candidate_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files (x86)/spwn/"
      - "C:/Users/<USER>/scoop/shims/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/AppData/Local/GeometryDash/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/GeodeSDK.GeodeCLI_Microsoft.Winget.Source_8wekyb3d8bbwe/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/Users/<USER>/AppData/Local/Programs/Windsurf/bin/"
    searched_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/mt.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/mt.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/mt"
      - "C:/Windows/System32/mt.com"
      - "C:/Windows/System32/mt.exe"
      - "C:/Windows/System32/mt"
      - "C:/Windows/mt.com"
      - "C:/Windows/mt.exe"
      - "C:/Windows/mt"
      - "C:/Windows/System32/wbem/mt.com"
      - "C:/Windows/System32/wbem/mt.exe"
      - "C:/Windows/System32/wbem/mt"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/mt.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/mt.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/mt"
      - "C:/Windows/System32/OpenSSH/mt.com"
      - "C:/Windows/System32/OpenSSH/mt.exe"
      - "C:/Windows/System32/OpenSSH/mt"
      - "C:/Program Files/Git/cmd/mt.com"
      - "C:/Program Files/Git/cmd/mt.exe"
      - "C:/Program Files/Git/cmd/mt"
      - "C:/Program Files/CMake/bin/mt.com"
      - "C:/Program Files/CMake/bin/mt.exe"
      - "C:/Program Files/CMake/bin/mt"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/mt.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/mt.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/mt"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/mt.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/mt.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/mt"
      - "C:/Program Files/nodejs/mt.com"
      - "C:/Program Files/nodejs/mt.exe"
      - "C:/Program Files/nodejs/mt"
      - "C:/Program Files (x86)/spwn/mt.com"
      - "C:/Program Files (x86)/spwn/mt.exe"
      - "C:/Program Files (x86)/spwn/mt"
      - "C:/Users/<USER>/scoop/shims/mt.com"
      - "C:/Users/<USER>/scoop/shims/mt.exe"
      - "C:/Users/<USER>/scoop/shims/mt"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/mt.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/mt.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/mt"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/mt.com"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/mt.exe"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/mt"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/mt.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/mt.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/mt"
      - "C:/Users/<USER>/AppData/Local/GeometryDash/mt.com"
      - "C:/Users/<USER>/AppData/Local/GeometryDash/mt.exe"
      - "C:/Users/<USER>/AppData/Local/GeometryDash/mt"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/GeodeSDK.GeodeCLI_Microsoft.Winget.Source_8wekyb3d8bbwe/mt.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/GeodeSDK.GeodeCLI_Microsoft.Winget.Source_8wekyb3d8bbwe/mt.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/GeodeSDK.GeodeCLI_Microsoft.Winget.Source_8wekyb3d8bbwe/mt"
      - "C:/Users/<USER>/AppData/Roaming/npm/mt.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/mt.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/mt"
      - "C:/Users/<USER>/AppData/Local/Programs/Windsurf/bin/mt.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Windsurf/bin/mt.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Windsurf/bin/mt"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files (x86)\\spwn\\"
        - "C:\\Users\\<USER>\\scoop\\shims"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\GeometryDash"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\GeodeSDK.GeodeCLI_Microsoft.Winget.Source_8wekyb3d8bbwe"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:7 (project)"
    mode: "program"
    variable: "CMAKE_AR"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "lib"
    candidate_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files (x86)/spwn/"
      - "C:/Users/<USER>/scoop/shims/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/AppData/Local/GeometryDash/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/GeodeSDK.GeodeCLI_Microsoft.Winget.Source_8wekyb3d8bbwe/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/Users/<USER>/AppData/Local/Programs/Windsurf/bin/"
    searched_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/lib.com"
    found: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/lib.exe"
    search_context:
      ENV{PATH}:
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files (x86)\\spwn\\"
        - "C:\\Users\\<USER>\\scoop\\shims"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\GeometryDash"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\GeodeSDK.GeodeCLI_Microsoft.Winget.Source_8wekyb3d8bbwe"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:7 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCXXCompilerId.cpp.in"
    candidate_directories:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/"
    found: "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCXXCompilerId.cpp.in"
    search_context:
      ENV{PATH}:
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files (x86)\\spwn\\"
        - "C:\\Users\\<USER>\\scoop\\shims"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\GeometryDash"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\GeodeSDK.GeodeCLI_Microsoft.Winget.Source_8wekyb3d8bbwe"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin"
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:7 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      Versión de MSBuild 17.14.14+a129329f1 para .NET Framework
      Compilación iniciada a las 24/7/2025 21:10:03.
      
      Proyecto "C:\\Users\\<USER>\\Downloads\\cerdoOINK-MOD\\build\\CMakeFiles\\4.1.0-rc1\\CompilerIdCXX\\CompilerIdCXX.vcxproj" en nodo 1 (destinos predeterminados).
      PrepareForBuild:
        Creando directorio "Debug\\".
        La salida estructurada está habilitada. El formato del diagnóstico del compilador reflejará la jerarquía de errores. Consulte https://aka.ms/cpp/structured-output para obtener más detalles.
        Creando directorio "Debug\\CompilerIdCXX.tlog\\".
      InitializeBuildStatus:
        Se creará "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild" porque se especificó "AlwaysCreate".
        Aplicando tarea Touch a "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCXX.lib" /MACHINE:X64 Debug\\CMakeCXXCompilerId.obj
        CompilerIdCXX.vcxproj -> C:\\Users\\<USER>\\Downloads\\cerdoOINK-MOD\\build\\CMakeFiles\\4.1.0-rc1\\CompilerIdCXX\\CompilerIdCXX.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_CXX_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        Se eliminará el archivo "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
        Aplicando tarea Touch a "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.lastbuildstate".
      Compilación del proyecto terminada "C:\\Users\\<USER>\\Downloads\\cerdoOINK-MOD\\build\\CMakeFiles\\4.1.0-rc1\\CompilerIdCXX\\CompilerIdCXX.vcxproj" (destinos predeterminados).
      
      Compilación correcta.
          0 Advertencia(s)
          0 Errores
      
      Tiempo transcurrido 00:00:00.71
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"
      
      The CXX compiler identification is MSVC, found in:
        C:/Users/<USER>/Downloads/cerdoOINK-MOD/build/CMakeFiles/4.1.0-rc1/CompilerIdCXX/CompilerIdCXX.exe
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:37 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:65 (__resolve_tool_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:104 (__resolve_linker_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:7 (project)"
    mode: "program"
    variable: "_CMAKE_TOOL_WITH_PATH"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "lld-link"
    candidate_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files (x86)/spwn/"
      - "C:/Users/<USER>/scoop/shims/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/AppData/Local/GeometryDash/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/GeodeSDK.GeodeCLI_Microsoft.Winget.Source_8wekyb3d8bbwe/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/Users/<USER>/AppData/Local/Programs/Windsurf/bin/"
    searched_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/lld-link.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/lld-link.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/lld-link"
      - "C:/Windows/System32/lld-link.com"
      - "C:/Windows/System32/lld-link.exe"
      - "C:/Windows/System32/lld-link"
      - "C:/Windows/lld-link.com"
      - "C:/Windows/lld-link.exe"
      - "C:/Windows/lld-link"
      - "C:/Windows/System32/wbem/lld-link.com"
      - "C:/Windows/System32/wbem/lld-link.exe"
      - "C:/Windows/System32/wbem/lld-link"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link"
      - "C:/Windows/System32/OpenSSH/lld-link.com"
      - "C:/Windows/System32/OpenSSH/lld-link.exe"
      - "C:/Windows/System32/OpenSSH/lld-link"
      - "C:/Program Files/Git/cmd/lld-link.com"
      - "C:/Program Files/Git/cmd/lld-link.exe"
      - "C:/Program Files/Git/cmd/lld-link"
      - "C:/Program Files/CMake/bin/lld-link.com"
      - "C:/Program Files/CMake/bin/lld-link.exe"
      - "C:/Program Files/CMake/bin/lld-link"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/lld-link.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/lld-link.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/lld-link"
      - "C:/Program Files/nodejs/lld-link.com"
      - "C:/Program Files/nodejs/lld-link.exe"
      - "C:/Program Files/nodejs/lld-link"
      - "C:/Program Files (x86)/spwn/lld-link.com"
      - "C:/Program Files (x86)/spwn/lld-link.exe"
      - "C:/Program Files (x86)/spwn/lld-link"
      - "C:/Users/<USER>/scoop/shims/lld-link.com"
      - "C:/Users/<USER>/scoop/shims/lld-link.exe"
      - "C:/Users/<USER>/scoop/shims/lld-link"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/lld-link"
      - "C:/Users/<USER>/AppData/Local/GeometryDash/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/GeometryDash/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/GeometryDash/lld-link"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/GeodeSDK.GeodeCLI_Microsoft.Winget.Source_8wekyb3d8bbwe/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/GeodeSDK.GeodeCLI_Microsoft.Winget.Source_8wekyb3d8bbwe/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/GeodeSDK.GeodeCLI_Microsoft.Winget.Source_8wekyb3d8bbwe/lld-link"
      - "C:/Users/<USER>/AppData/Roaming/npm/lld-link.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/lld-link.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Windsurf/bin/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Windsurf/bin/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Windsurf/bin/lld-link"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files (x86)\\spwn\\"
        - "C:\\Users\\<USER>\\scoop\\shims"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\GeometryDash"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\GeodeSDK.GeodeCLI_Microsoft.Winget.Source_8wekyb3d8bbwe"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineRCCompiler.cmake:40 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows-MSVC.cmake:573 (enable_language)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows-MSVC.cmake:546 (__windows_compiler_msvc_enable_rc)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows-MSVC-C.cmake:5 (__windows_compiler_msvc)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCInformation.cmake:48 (include)"
      - "CMakeLists.txt:7 (project)"
    mode: "program"
    variable: "CMAKE_RC_COMPILER"
    description: "RC compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "rc"
    candidate_directories:
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files (x86)/spwn/"
      - "C:/Users/<USER>/scoop/shims/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/AppData/Local/GeometryDash/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/GeodeSDK.GeodeCLI_Microsoft.Winget.Source_8wekyb3d8bbwe/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/Users/<USER>/AppData/Local/Programs/Windsurf/bin/"
      - "C:/Program Files/bin/"
      - "C:/Program Files/sbin/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/bin/"
      - "C:/Program Files (x86)/sbin/"
      - "C:/Program Files (x86)/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/CMake/sbin/"
      - "C:/Program Files/CMake/"
      - "C:/Program Files/cerdoOINK-MOD/bin/"
      - "C:/Program Files/cerdoOINK-MOD/sbin/"
      - "C:/Program Files/cerdoOINK-MOD/"
    searched_directories:
      - "C:/Windows/System32/rc.com"
      - "C:/Windows/System32/rc.exe"
      - "C:/Windows/System32/rc"
      - "C:/Windows/rc.com"
      - "C:/Windows/rc.exe"
      - "C:/Windows/rc"
      - "C:/Windows/System32/wbem/rc.com"
      - "C:/Windows/System32/wbem/rc.exe"
      - "C:/Windows/System32/wbem/rc"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/rc.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/rc.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/rc"
      - "C:/Windows/System32/OpenSSH/rc.com"
      - "C:/Windows/System32/OpenSSH/rc.exe"
      - "C:/Windows/System32/OpenSSH/rc"
      - "C:/Program Files/Git/cmd/rc.com"
      - "C:/Program Files/Git/cmd/rc.exe"
      - "C:/Program Files/Git/cmd/rc"
      - "C:/Program Files/CMake/bin/rc.com"
      - "C:/Program Files/CMake/bin/rc.exe"
      - "C:/Program Files/CMake/bin/rc"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/rc.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/rc.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/rc"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/rc.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/rc.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/rc"
      - "C:/Program Files/nodejs/rc.com"
      - "C:/Program Files/nodejs/rc.exe"
      - "C:/Program Files/nodejs/rc"
      - "C:/Program Files (x86)/spwn/rc.com"
      - "C:/Program Files (x86)/spwn/rc.exe"
      - "C:/Program Files (x86)/spwn/rc"
      - "C:/Users/<USER>/scoop/shims/rc.com"
      - "C:/Users/<USER>/scoop/shims/rc.exe"
      - "C:/Users/<USER>/scoop/shims/rc"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/rc.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/rc.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/rc"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/rc.com"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/rc.exe"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/rc"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/rc.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/rc.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/rc"
      - "C:/Users/<USER>/AppData/Local/GeometryDash/rc.com"
      - "C:/Users/<USER>/AppData/Local/GeometryDash/rc.exe"
      - "C:/Users/<USER>/AppData/Local/GeometryDash/rc"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/GeodeSDK.GeodeCLI_Microsoft.Winget.Source_8wekyb3d8bbwe/rc.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/GeodeSDK.GeodeCLI_Microsoft.Winget.Source_8wekyb3d8bbwe/rc.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/GeodeSDK.GeodeCLI_Microsoft.Winget.Source_8wekyb3d8bbwe/rc"
      - "C:/Users/<USER>/AppData/Roaming/npm/rc.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/rc.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/rc"
      - "C:/Users/<USER>/AppData/Local/Programs/Windsurf/bin/rc.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Windsurf/bin/rc.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Windsurf/bin/rc"
      - "C:/Program Files/bin/rc.com"
      - "C:/Program Files/bin/rc.exe"
      - "C:/Program Files/bin/rc"
      - "C:/Program Files/sbin/rc.com"
      - "C:/Program Files/sbin/rc.exe"
      - "C:/Program Files/sbin/rc"
      - "C:/Program Files/rc.com"
      - "C:/Program Files/rc.exe"
      - "C:/Program Files/rc"
      - "C:/Program Files (x86)/bin/rc.com"
      - "C:/Program Files (x86)/bin/rc.exe"
      - "C:/Program Files (x86)/bin/rc"
      - "C:/Program Files (x86)/sbin/rc.com"
      - "C:/Program Files (x86)/sbin/rc.exe"
      - "C:/Program Files (x86)/sbin/rc"
      - "C:/Program Files (x86)/rc.com"
      - "C:/Program Files (x86)/rc.exe"
      - "C:/Program Files (x86)/rc"
      - "C:/Program Files/CMake/bin/rc.com"
      - "C:/Program Files/CMake/bin/rc.exe"
      - "C:/Program Files/CMake/bin/rc"
      - "C:/Program Files/CMake/sbin/rc.com"
      - "C:/Program Files/CMake/sbin/rc.exe"
      - "C:/Program Files/CMake/sbin/rc"
      - "C:/Program Files/CMake/rc.com"
      - "C:/Program Files/CMake/rc.exe"
      - "C:/Program Files/CMake/rc"
      - "C:/Program Files/cerdoOINK-MOD/bin/rc.com"
      - "C:/Program Files/cerdoOINK-MOD/bin/rc.exe"
      - "C:/Program Files/cerdoOINK-MOD/bin/rc"
      - "C:/Program Files/cerdoOINK-MOD/sbin/rc.com"
      - "C:/Program Files/cerdoOINK-MOD/sbin/rc.exe"
      - "C:/Program Files/cerdoOINK-MOD/sbin/rc"
      - "C:/Program Files/cerdoOINK-MOD/rc.com"
      - "C:/Program Files/cerdoOINK-MOD/rc.exe"
      - "C:/Program Files/cerdoOINK-MOD/rc"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files (x86)\\spwn\\"
        - "C:\\Users\\<USER>\\scoop\\shims"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\GeometryDash"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\GeodeSDK.GeodeCLI_Microsoft.Winget.Source_8wekyb3d8bbwe"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin"
      CMAKE_INSTALL_PREFIX: "C:/Program Files/cerdoOINK-MOD"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/Program Files/CMake"
        - "C:/Program Files/cerdoOINK-MOD"
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:7 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "C:/Users/<USER>/Downloads/cerdoOINK-MOD/build/CMakeFiles/CMakeScratch/TryCompile-45hclw"
      binary: "C:/Users/<USER>/Downloads/cerdoOINK-MOD/build/CMakeFiles/CMakeScratch/TryCompile-45hclw"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_OSX_ARCHITECTURES: "arm64;x86_64"
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Downloads/cerdoOINK-MOD/build/CMakeFiles/CMakeScratch/TryCompile-45hclw'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_12335.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        Versión de MSBuild 17.14.14+a129329f1 para .NET Framework
        Compilación iniciada a las 24/7/2025 21:10:04.
        
        Proyecto "C:\\Users\\<USER>\\Downloads\\cerdoOINK-MOD\\build\\CMakeFiles\\CMakeScratch\\TryCompile-45hclw\\cmTC_12335.vcxproj" en nodo 1 (destinos predeterminados).
        PrepareForBuild:
          Creando directorio "cmTC_12335.dir\\Debug\\".
          La salida estructurada está habilitada. El formato del diagnóstico del compilador reflejará la jerarquía de errores. Consulte https://aka.ms/cpp/structured-output para obtener más detalles.
          Creando directorio "C:\\Users\\<USER>\\Downloads\\cerdoOINK-MOD\\build\\CMakeFiles\\CMakeScratch\\TryCompile-45hclw\\Debug\\".
          Creando directorio "cmTC_12335.dir\\Debug\\cmTC_12335.tlog\\".
        InitializeBuildStatus:
          Se creará "cmTC_12335.dir\\Debug\\cmTC_12335.tlog\\unsuccessfulbuild" porque se especificó "AlwaysCreate".
          Aplicando tarea Touch a "cmTC_12335.dir\\Debug\\cmTC_12335.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Fo"cmTC_12335.dir\\Debug\\\\" /Fd"cmTC_12335.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.1\\Modules\\CMakeCCompilerABI.c"
          Compilador de optimización de C/C++ de Microsoft (R) versión 19.44.35211 para x64
          (C) Microsoft Corporation. Todos los derechos reservados.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Fo"cmTC_12335.dir\\Debug\\\\" /Fd"cmTC_12335.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.1\\Modules\\CMakeCCompilerABI.c"
          CMakeCCompilerABI.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /OUT:"C:\\Users\\<USER>\\Downloads\\cerdoOINK-MOD\\build\\CMakeFiles\\CMakeScratch\\TryCompile-45hclw\\Debug\\cmTC_12335.exe" /INCREMENTAL /ILK:"cmTC_12335.dir\\Debug\\cmTC_12335.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/Downloads/cerdoOINK-MOD/build/CMakeFiles/CMakeScratch/TryCompile-45hclw/Debug/cmTC_12335.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /IMPLIB:"C:/Users/<USER>/Downloads/cerdoOINK-MOD/build/CMakeFiles/CMakeScratch/TryCompile-45hclw/Debug/cmTC_12335.lib" /MACHINE:X64  /machine:x64 cmTC_12335.dir\\Debug\\CMakeCCompilerABI.obj
          cmTC_12335.vcxproj -> C:\\Users\\<USER>\\Downloads\\cerdoOINK-MOD\\build\\CMakeFiles\\CMakeScratch\\TryCompile-45hclw\\Debug\\cmTC_12335.exe
        FinalizeBuildStatus:
          Se eliminará el archivo "cmTC_12335.dir\\Debug\\cmTC_12335.tlog\\unsuccessfulbuild".
          Aplicando tarea Touch a "cmTC_12335.dir\\Debug\\cmTC_12335.tlog\\cmTC_12335.lastbuildstate".
        Compilación del proyecto terminada "C:\\Users\\<USER>\\Downloads\\cerdoOINK-MOD\\build\\CMakeFiles\\CMakeScratch\\TryCompile-45hclw\\cmTC_12335.vcxproj" (destinos predeterminados).
        
        Compilación correcta.
            0 Advertencia(s)
            0 Errores
        
        Tiempo transcurrido 00:00:00.78
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:253 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:7 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'C': C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:299 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:7 (project)"
    message: |
      Running the C compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.44.35211.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:7 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "C:/Users/<USER>/Downloads/cerdoOINK-MOD/build/CMakeFiles/CMakeScratch/TryCompile-s30o97"
      binary: "C:/Users/<USER>/Downloads/cerdoOINK-MOD/build/CMakeFiles/CMakeScratch/TryCompile-s30o97"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_OSX_ARCHITECTURES: "arm64;x86_64"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Downloads/cerdoOINK-MOD/build/CMakeFiles/CMakeScratch/TryCompile-s30o97'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_dd323.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        Versión de MSBuild 17.14.14+a129329f1 para .NET Framework
        Compilación iniciada a las 24/7/2025 21:10:06.
        
        Proyecto "C:\\Users\\<USER>\\Downloads\\cerdoOINK-MOD\\build\\CMakeFiles\\CMakeScratch\\TryCompile-s30o97\\cmTC_dd323.vcxproj" en nodo 1 (destinos predeterminados).
        PrepareForBuild:
          Creando directorio "cmTC_dd323.dir\\Debug\\".
          La salida estructurada está habilitada. El formato del diagnóstico del compilador reflejará la jerarquía de errores. Consulte https://aka.ms/cpp/structured-output para obtener más detalles.
          Creando directorio "C:\\Users\\<USER>\\Downloads\\cerdoOINK-MOD\\build\\CMakeFiles\\CMakeScratch\\TryCompile-s30o97\\Debug\\".
          Creando directorio "cmTC_dd323.dir\\Debug\\cmTC_dd323.tlog\\".
        InitializeBuildStatus:
          Se creará "cmTC_dd323.dir\\Debug\\cmTC_dd323.tlog\\unsuccessfulbuild" porque se especificó "AlwaysCreate".
          Aplicando tarea Touch a "cmTC_dd323.dir\\Debug\\cmTC_dd323.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /std:c++20 /Fo"cmTC_dd323.dir\\Debug\\\\" /Fd"cmTC_dd323.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.1\\Modules\\CMakeCXXCompilerABI.cpp"
          Compilador de optimización de C/C++ de Microsoft (R) versión 19.44.35211 para x64
          (C) Microsoft Corporation. Todos los derechos reservados.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /std:c++20 /Fo"cmTC_dd323.dir\\Debug\\\\" /Fd"cmTC_dd323.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.1\\Modules\\CMakeCXXCompilerABI.cpp"
          CMakeCXXCompilerABI.cpp
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /OUT:"C:\\Users\\<USER>\\Downloads\\cerdoOINK-MOD\\build\\CMakeFiles\\CMakeScratch\\TryCompile-s30o97\\Debug\\cmTC_dd323.exe" /INCREMENTAL /ILK:"cmTC_dd323.dir\\Debug\\cmTC_dd323.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/Downloads/cerdoOINK-MOD/build/CMakeFiles/CMakeScratch/TryCompile-s30o97/Debug/cmTC_dd323.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /IMPLIB:"C:/Users/<USER>/Downloads/cerdoOINK-MOD/build/CMakeFiles/CMakeScratch/TryCompile-s30o97/Debug/cmTC_dd323.lib" /MACHINE:X64  /machine:x64 cmTC_dd323.dir\\Debug\\CMakeCXXCompilerABI.obj
          cmTC_dd323.vcxproj -> C:\\Users\\<USER>\\Downloads\\cerdoOINK-MOD\\build\\CMakeFiles\\CMakeScratch\\TryCompile-s30o97\\Debug\\cmTC_dd323.exe
        FinalizeBuildStatus:
          Se eliminará el archivo "cmTC_dd323.dir\\Debug\\cmTC_dd323.tlog\\unsuccessfulbuild".
          Aplicando tarea Touch a "cmTC_dd323.dir\\Debug\\cmTC_dd323.tlog\\cmTC_dd323.lastbuildstate".
        Compilación del proyecto terminada "C:\\Users\\<USER>\\Downloads\\cerdoOINK-MOD\\build\\CMakeFiles\\CMakeScratch\\TryCompile-s30o97\\cmTC_dd323.vcxproj" (destinos predeterminados).
        
        Compilación correcta.
            0 Advertencia(s)
            0 Errores
        
        Tiempo transcurrido 00:00:00.71
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:253 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:7 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'CXX': C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:299 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:7 (project)"
    message: |
      Running the CXX compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.44.35211.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "find-v1"
    backtrace:
      - "C:/Users/<USER>/mod-dev/CMakeLists.txt:19 (find_program)"
    mode: "program"
    variable: "GIT_EXECUTABLE"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "git"
    candidate_directories:
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files (x86)/spwn/"
      - "C:/Users/<USER>/scoop/shims/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/AppData/Local/GeometryDash/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/GeodeSDK.GeodeCLI_Microsoft.Winget.Source_8wekyb3d8bbwe/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/Users/<USER>/AppData/Local/Programs/Windsurf/bin/"
      - "C:/Program Files/bin/"
      - "C:/Program Files/sbin/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/bin/"
      - "C:/Program Files (x86)/sbin/"
      - "C:/Program Files (x86)/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/CMake/sbin/"
      - "C:/Program Files/CMake/"
      - "C:/Program Files/cerdoOINK-MOD/bin/"
      - "C:/Program Files/cerdoOINK-MOD/sbin/"
      - "C:/Program Files/cerdoOINK-MOD/"
    searched_directories:
      - "C:/Windows/System32/git.com"
      - "C:/Windows/System32/git.exe"
      - "C:/Windows/System32/git"
      - "C:/Windows/git.com"
      - "C:/Windows/git.exe"
      - "C:/Windows/git"
      - "C:/Windows/System32/wbem/git.com"
      - "C:/Windows/System32/wbem/git.exe"
      - "C:/Windows/System32/wbem/git"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/git.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/git.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/git"
      - "C:/Windows/System32/OpenSSH/git.com"
      - "C:/Windows/System32/OpenSSH/git.exe"
      - "C:/Windows/System32/OpenSSH/git"
      - "C:/Program Files/Git/cmd/git.com"
    found: "C:/Program Files/Git/cmd/git.exe"
    search_context:
      ENV{PATH}:
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files (x86)\\spwn\\"
        - "C:\\Users\\<USER>\\scoop\\shims"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\GeometryDash"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\GeodeSDK.GeodeCLI_Microsoft.Winget.Source_8wekyb3d8bbwe"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin"
      CMAKE_INSTALL_PREFIX: "C:/Program Files/cerdoOINK-MOD"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/Program Files/CMake"
        - "C:/Program Files/cerdoOINK-MOD"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Users/<USER>/mod-dev/cmake/GeodeFile.cmake:5 (find_program)"
      - "C:/Users/<USER>/mod-dev/CMakeLists.txt:195 (include)"
    mode: "program"
    variable: "GEODE_CLI"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "geode.exe"
      - "geode-cli.exe"
      - "geode"
      - "geode-cli"
    candidate_directories:
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files (x86)/spwn/"
      - "C:/Users/<USER>/scoop/shims/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/AppData/Local/GeometryDash/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/GeodeSDK.GeodeCLI_Microsoft.Winget.Source_8wekyb3d8bbwe/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/Users/<USER>/AppData/Local/Programs/Windsurf/bin/"
      - "C:/Program Files/bin/"
      - "C:/Program Files/sbin/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/bin/"
      - "C:/Program Files (x86)/sbin/"
      - "C:/Program Files (x86)/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/CMake/sbin/"
      - "C:/Program Files/CMake/"
      - "C:/Program Files/cerdoOINK-MOD/bin/"
      - "C:/Program Files/cerdoOINK-MOD/sbin/"
      - "C:/Program Files/cerdoOINK-MOD/"
    searched_directories:
      - "C:/Windows/System32/geode.exe.com"
      - "C:/Windows/System32/geode.exe"
      - "C:/Windows/geode.exe.com"
      - "C:/Windows/geode.exe"
      - "C:/Windows/System32/wbem/geode.exe.com"
      - "C:/Windows/System32/wbem/geode.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/geode.exe.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/geode.exe"
      - "C:/Windows/System32/OpenSSH/geode.exe.com"
      - "C:/Windows/System32/OpenSSH/geode.exe"
      - "C:/Program Files/Git/cmd/geode.exe.com"
      - "C:/Program Files/Git/cmd/geode.exe"
      - "C:/Program Files/CMake/bin/geode.exe.com"
      - "C:/Program Files/CMake/bin/geode.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/geode.exe.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/geode.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/geode.exe.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/geode.exe"
      - "C:/Program Files/nodejs/geode.exe.com"
      - "C:/Program Files/nodejs/geode.exe"
      - "C:/Program Files (x86)/spwn/geode.exe.com"
      - "C:/Program Files (x86)/spwn/geode.exe"
      - "C:/Users/<USER>/scoop/shims/geode.exe.com"
    found: "C:/Users/<USER>/scoop/shims/geode.exe"
    search_context:
      ENV{PATH}:
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files (x86)\\spwn\\"
        - "C:\\Users\\<USER>\\scoop\\shims"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\GeometryDash"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\GeodeSDK.GeodeCLI_Microsoft.Winget.Source_8wekyb3d8bbwe"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin"
      CMAKE_INSTALL_PREFIX: "C:/Program Files/cerdoOINK-MOD"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/Program Files/CMake"
        - "C:/Program Files/cerdoOINK-MOD"
...
