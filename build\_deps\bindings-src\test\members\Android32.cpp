#include "Common.hpp"

#ifdef GEODE_IS_ANDROID32

using namespace geode::prelude;

// Add known android struct members here

GEODE_SIZE_CHECK(CCObject, 0x34);
GEODE_SIZE_CHECK(CCNode, 0x108);
GEODE_SIZE_CHECK(CCNodeRGBA, 0x118);
GEODE_SIZE_CHECK(CCLayer, 0x13c);
GEODE_SIZE_CHECK(CCLayerRGBA, 0x14c);
GEODE_SIZE_CHECK(CCLayerColor, 0x1b8);
GEODE_SIZE_CHECK(CCSprite, 0x1fc);

GEODE_SIZE_CHECK(CCMenu, 0x154);
GEODE_SIZE_CHECK(CCMenuItem, 0x12c);
GEODE_SIZE_CHECK(CCMenuItemSprite, 0x138);

GEODE_SIZE_CHECK(CCScale9Sprite, 0x1a4);

GEODE_SIZE_CHECK(FL<PERSON>lertLayer, 0x1f0);
GEODE_SIZE_CHECK(BoomListView, 0x164);
GEODE_SIZE_CHECK(CustomListView, 0x168);
GEODE_SIZE_CHECK(CCMenuItemSpriteExtra, 0x174);
GEODE_SIZE_CHECK(LoadingLayer, 0x15c);
GEODE_SIZE_CHECK(GJDropDownLayer, 0x1e0);
GEODE_SIZE_CHECK(TableViewCell, 0x19c);
GEODE_SIZE_CHECK(Slider, 0x158);
GEODE_SIZE_CHECK(SliderTouchLogic, 0x174);
GEODE_SIZE_CHECK(CCScrollLayerExt, 0x184);
GEODE_SIZE_CHECK(TableView, 0x1dc);
GEODE_SIZE_CHECK(CCTextInputNode, 0x1a0);
GEODE_SIZE_CHECK(CCTouchDispatcher, 0x6c);
GEODE_SIZE_CHECK(CCSpritePlus, 0x208);

GEODE_SIZE_CHECK(TextArea, 0x248);
GEODE_MEMBER_CHECK(TextArea, m_disableColor, 0x1fc);
GEODE_MEMBER_CHECK(TextArea, m_label, 0x200);
GEODE_MEMBER_CHECK(TextArea, m_width, 0x204);
GEODE_MEMBER_CHECK(TextArea, m_unknown, 0x208);
GEODE_MEMBER_CHECK(TextArea, m_fontFile, 0x20c);
GEODE_MEMBER_CHECK(TextArea, m_height, 0x210);
GEODE_MEMBER_CHECK(TextArea, m_unkBool, 0x214);
GEODE_MEMBER_CHECK(TextArea, m_anchorPoint, 0x218);
GEODE_MEMBER_CHECK(TextArea, m_allShown, 0x220);
GEODE_MEMBER_CHECK(TextArea, m_scale, 0x224);
GEODE_MEMBER_CHECK(TextArea, m_rectHeight, 0x228);
GEODE_MEMBER_CHECK(TextArea, m_rectWidth, 0x22c);
GEODE_MEMBER_CHECK(TextArea, m_maxWidth, 0x230);
GEODE_MEMBER_CHECK(TextArea, m_unkPoint, 0x234);
GEODE_MEMBER_CHECK(TextArea, m_delegate, 0x23c);
GEODE_MEMBER_CHECK(TextArea, m_shakeCharacters, 0x240);
GEODE_MEMBER_CHECK(TextArea, m_shakeElapsed, 0x244);

// GEODE_MEMBER_CHECK(CCTouchDispatcher, m_forcePrio, 0x60);

GEODE_MEMBER_CHECK(CCTextInputNode, m_maxLabelWidth, 0x164);
GEODE_MEMBER_CHECK(CCTextInputNode, m_textField, 0x17c);
GEODE_MEMBER_CHECK(FLAlertLayer, m_buttonMenu, 0x1b8);
GEODE_MEMBER_CHECK(FLAlertLayer, m_mainLayer, 0x1c8);
GEODE_MEMBER_CHECK(TableView, m_cellArray, 0x1b0);
GEODE_MEMBER_CHECK(TableViewCell, m_indexPath, 0x144);
GEODE_MEMBER_CHECK(TableViewCell, m_unknownString, 0x184);
GEODE_MEMBER_CHECK(TableViewCell, m_backgroundLayer, 0x190);
GEODE_SIZE_CHECK(CCBlockLayer, 0x1bc);


GEODE_SIZE_CHECK(GJBaseGameLayer, 0x2d68);
GEODE_MEMBER_CHECK(GJBaseGameLayer, m_player1, 0x894);
GEODE_MEMBER_CHECK(GJBaseGameLayer, m_player2, 0x898);
GEODE_MEMBER_CHECK(GJBaseGameLayer, m_groupDict, 0x95c);
GEODE_MEMBER_CHECK(GJBaseGameLayer, m_parentGroupsDict, 0x98c);
GEODE_MEMBER_CHECK(GJBaseGameLayer, m_varianceValues, 0xa48);
GEODE_MEMBER_CHECK(GJBaseGameLayer, m_particlesDict, 0x2a04);
GEODE_MEMBER_CHECK(GJBaseGameLayer, m_customParticles, 0x2a08);
GEODE_MEMBER_CHECK(GJBaseGameLayer, m_unclaimedParticles, 0x2a0c);
GEODE_MEMBER_CHECK(GJBaseGameLayer, m_claimedParticles, 0x2a2c);
GEODE_MEMBER_CHECK(GJBaseGameLayer, m_temporaryParticles, 0x2a30);
GEODE_MEMBER_CHECK(GJBaseGameLayer, m_attempts, 0x29d4);
GEODE_MEMBER_CHECK(GJBaseGameLayer, m_gradientLayers, 0x2a50);
GEODE_MEMBER_CHECK(GJBaseGameLayer, m_startPosObject, 0x2a60);
GEODE_MEMBER_CHECK(GJBaseGameLayer, m_flashNode, 0x2aa4);
GEODE_MEMBER_CHECK(GJBaseGameLayer, m_audioEffectsLayer, 0x2b04);
GEODE_MEMBER_CHECK(GJBaseGameLayer, m_queuedButtons, 0x2b5c);
GEODE_MEMBER_CHECK(GJBaseGameLayer, m_uiLayer, 0x2c10);
GEODE_MEMBER_CHECK(GJBaseGameLayer, m_sections, 0x2c34);
GEODE_MEMBER_CHECK(GJBaseGameLayer, m_sectionSizes, 0x2c88);
GEODE_MEMBER_CHECK(GJBaseGameLayer, m_audioVisualizerBG, 0x2ce4);
GEODE_MEMBER_CHECK(GJBaseGameLayer, m_loadingLayer, 0x2d54);
GEODE_MEMBER_CHECK(GJBaseGameLayer, m_debugDrawNode, 0x2d58);
// GEODE_MEMBER_CHECK(GJBaseGameLayer, m_started, 0x2c28); // i dont even know anymore

GEODE_SIZE_CHECK(PlayLayer, 0x2f68);
GEODE_MEMBER_CHECK(PlayLayer, m_circleWaveArray, 0x2e24);
GEODE_MEMBER_CHECK(PlayLayer, m_attemptLabel, 0x2e38);
GEODE_MEMBER_CHECK(PlayLayer, m_progressBar, 0x2e44);
GEODE_MEMBER_CHECK(PlayLayer, m_jumps, 0x2e8c);
GEODE_MEMBER_CHECK(PlayLayer, m_hasCompletedLevel, 0x2e99);
GEODE_MEMBER_CHECK(PlayLayer, m_isPaused, 0x2f07);

GEODE_SIZE_CHECK(DrawGridLayer, 0x1c0);
GEODE_MEMBER_CHECK(DrawGridLayer, m_editorLayer, 0x17c);
GEODE_MEMBER_CHECK(DrawGridLayer, m_gridSize, 0x1bc);

GEODE_SIZE_CHECK(LevelEditorLayer, 0x2f48);
GEODE_MEMBER_CHECK(LevelEditorLayer, m_trailTimer, 0x2e08);
GEODE_MEMBER_CHECK(LevelEditorLayer, m_drawGridLayer, 0x2e28);

GEODE_SIZE_CHECK(GameObject, 0x488);
GEODE_MEMBER_CHECK(GameObject, m_particleString, 0x298);

GEODE_SIZE_CHECK(EnhancedGameObject, 0x4f0);
GEODE_SIZE_CHECK(EffectGameObject, 0x668);
GEODE_SIZE_CHECK(TextGameObject, 0x490);
GEODE_SIZE_CHECK(SmartGameObject, 0x490);
GEODE_SIZE_CHECK(ParticleGameObject, 0x618);
GEODE_SIZE_CHECK(SpecialAnimGameObject, 0x4f0);
GEODE_SIZE_CHECK(RingObject, 0x668);
GEODE_SIZE_CHECK(StartPosObject, 0x668);
GEODE_SIZE_CHECK(LabelGameObject, 0x690);
GEODE_SIZE_CHECK(TeleportPortalObject, 0x6a0);

GEODE_SIZE_CHECK(PlayerObject, 0x9c8);
GEODE_MEMBER_CHECK(PlayerObject, m_particleSystems, 0x580);
GEODE_MEMBER_CHECK(PlayerObject, m_isHidden, 0x5cb);
GEODE_MEMBER_CHECK(PlayerObject, m_ghostTrail, 0x5d0);
GEODE_MEMBER_CHECK(PlayerObject, m_waveTrail, 0x610);
GEODE_MEMBER_CHECK(PlayerObject, m_trailingParticleLife, 0x630);
GEODE_MEMBER_CHECK(PlayerObject, m_padRingRelated, 0x640);
GEODE_MEMBER_CHECK(PlayerObject, m_switchWaveTrailColor, 0x68a);
GEODE_MEMBER_CHECK(PlayerObject, m_ringRelatedSet, 0x6a4);
GEODE_MEMBER_CHECK(PlayerObject, m_objectSnappedTo, 0x6c0);
GEODE_MEMBER_CHECK(PlayerObject, m_spiderSprite, 0x6d0);
GEODE_MEMBER_CHECK(PlayerObject, m_playerGroundParticles, 0x6d8);
GEODE_MEMBER_CHECK(PlayerObject, m_landParticles1, 0x704);
GEODE_MEMBER_CHECK(PlayerObject, m_hasCustomGlowColor, 0x770);
GEODE_MEMBER_CHECK(PlayerObject, m_isDart, 0x7ac);
GEODE_MEMBER_CHECK(PlayerObject, m_vehicleSize, 0x7e0);
GEODE_MEMBER_CHECK(PlayerObject, m_playerSpeed, 0x7e4);
GEODE_MEMBER_CHECK(PlayerObject, m_shipRotation, 0x7e8);
GEODE_MEMBER_CHECK(PlayerObject, m_lastGroundedPos, 0x81c);
GEODE_MEMBER_CHECK(PlayerObject, m_touchingRings, 0x824);
GEODE_MEMBER_CHECK(PlayerObject, m_position, 0x850);
GEODE_MEMBER_CHECK(PlayerObject, m_playerFollowFloats, 0x880);
GEODE_MEMBER_CHECK(PlayerObject, m_platformerXVelocity, 0x8a8);
GEODE_MEMBER_CHECK(PlayerObject, m_isPlatformer, 0x920);
GEODE_MEMBER_CHECK(PlayerObject, m_currentRobotAnimation, 0x988);
GEODE_MEMBER_CHECK(PlayerObject, m_gameLayer, 0x9a8);
GEODE_MEMBER_CHECK(PlayerObject, m_parentLayer, 0x9ac);
GEODE_MEMBER_CHECK(PlayerObject, m_actionManager, 0x9b0);



// following are from 2.205 so might not all be right

GEODE_SIZE_CHECK(FMODAudioEngine, 0x4b0);
GEODE_SIZE_CHECK(FMODAudioState, 0x1c8);
GEODE_MEMBER_CHECK(FMODAudioEngine, m_musicVolume, 0x15c);
GEODE_MEMBER_CHECK(FMODAudioEngine, m_sfxVolume, 0x160);
GEODE_MEMBER_CHECK(FMODAudioEngine, m_pulse1, 0x16c);
GEODE_MEMBER_CHECK(FMODAudioEngine, m_pulse2, 0x170);
GEODE_MEMBER_CHECK(FMODAudioEngine, m_pulse3, 0x174);
GEODE_MEMBER_CHECK(FMODAudioEngine, m_metering, 0x17c);
GEODE_MEMBER_CHECK(FMODAudioEngine, m_system, 0x184);
GEODE_MEMBER_CHECK(FMODAudioEngine, m_mainDSP, 0x188);
GEODE_MEMBER_CHECK(FMODAudioEngine, m_globalChannelDSP, 0x18c);
GEODE_MEMBER_CHECK(FMODAudioEngine, m_globalChannel, 0x190);
GEODE_MEMBER_CHECK(FMODAudioEngine, m_reverbChannel, 0x194);
GEODE_MEMBER_CHECK(FMODAudioEngine, m_lastResult, 0x198);
GEODE_MEMBER_CHECK(FMODAudioEngine, m_sampleRate, 0x19c);
GEODE_MEMBER_CHECK(FMODAudioEngine, m_reducedQuality, 0x1a0);
GEODE_MEMBER_CHECK(FMODAudioEngine, m_audioState, 0x1b0);
GEODE_MEMBER_CHECK(FMODAudioEngine, m_removedSounds, 0x378);
GEODE_MEMBER_CHECK(FMODAudioEngine, m_queuedEffects, 0x430);
GEODE_MEMBER_CHECK(FMODAudioEngine, m_reverbChannelGroups, 0x474);

GEODE_MEMBER_CHECK(GameManager, m_playLayer, 0x16c);
GEODE_MEMBER_CHECK(GameManager, m_gameLayer, 0x174);

GEODE_SIZE_CHECK(EditorUI, 0x3b8);
GEODE_MEMBER_CHECK(EditorUI, m_selectedObjects, 0x260);
GEODE_MEMBER_CHECK(EditorUI, m_selectedObject, 0x370);
GEODE_MEMBER_CHECK(EditorUI, m_customTabBar, 0x1c0);
GEODE_MEMBER_CHECK(EditorUI, m_transformControl, 0x218);
GEODE_MEMBER_CHECK(EditorUI, m_editButtonBar, 0x22c);
GEODE_MEMBER_CHECK(EditorUI, m_positionSlider, 0x230);
GEODE_MEMBER_CHECK(EditorUI, m_transformState, 0x174);
GEODE_MEMBER_CHECK(EditorUI, m_editButtonBar, 0x22c);
GEODE_MEMBER_CHECK(EditorUI, m_positionSlider, 0x230);
GEODE_MEMBER_CHECK(EditorUI, m_selectedObjects, 0x260);
GEODE_MEMBER_CHECK(EditorUI, m_selectedMode, 0x338);
GEODE_MEMBER_CHECK(EditorUI, m_selectedObject, 0x370);

GEODE_MEMBER_CHECK(GameObject, m_hasExtendedCollision, 0x218);
GEODE_MEMBER_CHECK(GameObject, m_scaleX, 0x3e8);

GEODE_SIZE_CHECK(SetupTriggerPopup, 0x2a8);
GEODE_SIZE_CHECK(GJOptionsLayer, 0x2d8);

GEODE_MEMBER_CHECK(ColorSelectPopup, m_colorAction, 0x2f0);

GEODE_MEMBER_CHECK(ChallengesPage, m_dots, 0x1FC);
GEODE_MEMBER_CHECK(ChallengesPage, m_challengeNodes, 0x210);

GEODE_SIZE_CHECK(GJEffectManager, 0x42c);
GEODE_MEMBER_CHECK(GJEffectManager, m_colorActionDict, 0x198);
GEODE_MEMBER_CHECK(GJEffectManager, m_itemCountMap, 0x1f4);
GEODE_MEMBER_CHECK(GJEffectManager, m_persistentItemCountMap, 0x210);
GEODE_MEMBER_CHECK(GJEffectManager, m_persistentTimerItemSet, 0x22c);
GEODE_MEMBER_CHECK(GJEffectManager, m_timerItemMap, 0x248);

GEODE_MEMBER_CHECK(HardStreak, m_pointArray, 0x158);

GEODE_MEMBER_CHECK(GJShaderState, m_time, 0x38);
GEODE_MEMBER_CHECK(GJShaderState, m_prevTime, 0x40);
GEODE_MEMBER_CHECK(GJShaderState, m_lensCircleTintR, 0x176);
GEODE_MEMBER_CHECK(GJShaderState, m_lensCircleTintG, 0x177);
GEODE_MEMBER_CHECK(GJShaderState, m_lensCircleTintB, 0x178);

GEODE_MEMBER_CHECK(ShaderLayer, m_shader, 0x3c4);
GEODE_MEMBER_CHECK(ShaderLayer, m_gameLayer, 0x3d8);
GEODE_MEMBER_CHECK(ShaderLayer, m_splitYRangeMultUniform, 0x5a8);

GEODE_MEMBER_CHECK(SetupInstantCountPopup, m_itemID, 0x2b4);

GEODE_SIZE_CHECK(GJValueTween, 0x28);
GEODE_SIZE_CHECK(GameObjectPhysics, 0x28);
GEODE_SIZE_CHECK(EventTriggerInstance, 0x1c);

GEODE_SIZE_CHECK(EnterEffectInstance, 0xd4);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_enterEffectAnimMap, 0x0);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_length, 0x18);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_lengthVariance, 0x1c);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_offset, 0x20);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_offsetVariance, 0x24);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_offsetY, 0x28);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_offsetYVariance, 0x2c);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_modFront, 0x30);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_modBack, 0x34);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_deadzone, 0x38);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_moveDistance, 0x3c);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_moveDistanceVariance, 0x40);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_moveAngle, 0x44);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_moveAngleVariance, 0x48);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_moveX, 0x4c);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_moveXVariance, 0x50);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_moveY, 0x54);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_moveYVariance, 0x58);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_relativeFade, 0x5c);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_scaleX, 0x60);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_scaleXVariance, 0x64);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_scaleY, 0x68);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_scaleYVariance, 0x6c);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_rotation, 0x70);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_rotationVariance, 0x74);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_tint, 0x78);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_unk074, 0x7c);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_toOpacity, 0x80);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_fromOpacity, 0x84);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_hsvValue, 0x88);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_hue, 0x98);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_saturation, 0x9c);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_value, 0xa0);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_gameObject, 0xa4);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_unkBool1, 0xa8);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_targetID, 0xac);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_centerID, 0xb0);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_unkFloat3, 0xb4);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_unkFloat4, 0xb8);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_unkBool2, 0xbc);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_unkBool3, 0xbd);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_unkBool4, 0xbe);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_unkFloat5, 0xc0);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_unkVecInt, 0xc4);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_controlID, 0xd0);

GEODE_SIZE_CHECK(AdvancedFollowInstance, 0x1c);
GEODE_MEMBER_CHECK(AdvancedFollowInstance, m_gameObject, 0x0);
GEODE_MEMBER_CHECK(AdvancedFollowInstance, m_group, 0x4);
GEODE_MEMBER_CHECK(AdvancedFollowInstance, m_objectKey, 0x8);
GEODE_MEMBER_CHECK(AdvancedFollowInstance, m_controlId, 0xc);
GEODE_MEMBER_CHECK(AdvancedFollowInstance, m_otherObjectKey, 0x10);
GEODE_MEMBER_CHECK(AdvancedFollowInstance, m_relatedToGJGameStateUnkUint7, 0x14);
GEODE_MEMBER_CHECK(AdvancedFollowInstance, m_finished, 0x18);
GEODE_MEMBER_CHECK(AdvancedFollowInstance, m_doStart, 0x19);
GEODE_MEMBER_CHECK(AdvancedFollowInstance, m_started, 0x1a);
GEODE_MEMBER_CHECK(AdvancedFollowInstance, m_processed, 0x1b);

GEODE_SIZE_CHECK(DynamicObjectAction, 0x3c);
GEODE_MEMBER_CHECK(DynamicObjectAction, m_gameObject1, 0x0);
GEODE_MEMBER_CHECK(DynamicObjectAction, m_gameObject2, 0x4);
GEODE_MEMBER_CHECK(DynamicObjectAction, m_gameObject3, 0x8);
GEODE_MEMBER_CHECK(DynamicObjectAction, m_gameObject4, 0xc);
GEODE_MEMBER_CHECK(DynamicObjectAction, m_gameObject5, 0x10);
GEODE_MEMBER_CHECK(DynamicObjectAction, m_gameObject6, 0x14);
GEODE_MEMBER_CHECK(DynamicObjectAction, m_gameObject7, 0x18);
GEODE_MEMBER_CHECK(DynamicObjectAction, m_gameObject8, 0x1c);
GEODE_MEMBER_CHECK(DynamicObjectAction, m_unkFloat1, 0x20);
GEODE_MEMBER_CHECK(DynamicObjectAction, m_unkFloat2, 0x24);
GEODE_MEMBER_CHECK(DynamicObjectAction, m_unkFloat3, 0x28);
GEODE_MEMBER_CHECK(DynamicObjectAction, m_unkBool1, 0x2c);
GEODE_MEMBER_CHECK(DynamicObjectAction, m_unkBool2, 0x2d);
GEODE_MEMBER_CHECK(DynamicObjectAction, m_unkBool3, 0x2e);
GEODE_MEMBER_CHECK(DynamicObjectAction, m_unkBool4, 0x2f);
GEODE_MEMBER_CHECK(DynamicObjectAction, m_unkFloat4, 0x30);
GEODE_MEMBER_CHECK(DynamicObjectAction, m_unkFloat5, 0x34);
GEODE_MEMBER_CHECK(DynamicObjectAction, m_unkFloat6, 0x38);

GEODE_SIZE_CHECK(SFXTriggerInstance, 0x10);
GEODE_MEMBER_CHECK(SFXTriggerInstance, m_groupID1, 0x0);
GEODE_MEMBER_CHECK(SFXTriggerInstance, m_groupID2, 0x4);
GEODE_MEMBER_CHECK(SFXTriggerInstance, m_controlID, 0x8);
GEODE_MEMBER_CHECK(SFXTriggerInstance, m_sfxTriggerGameObject, 0xc);

GEODE_SIZE_CHECK(SongChannelState, 0x20);
GEODE_MEMBER_CHECK(SongChannelState, m_songTriggerGameObject1, 0x0);
GEODE_MEMBER_CHECK(SongChannelState, m_unkDouble1, 0x8);
GEODE_MEMBER_CHECK(SongChannelState, m_songTriggerGameObject2, 0x10);
GEODE_MEMBER_CHECK(SongChannelState, m_unkDouble2, 0x18);

GEODE_SIZE_CHECK(SongTriggerState, 0x10);
GEODE_MEMBER_CHECK(SongTriggerState, m_songTriggerGameObject, 0x0);
GEODE_MEMBER_CHECK(SongTriggerState, m_unkDouble, 0x8);

GEODE_SIZE_CHECK(SFXStateContainer, 0x20);
GEODE_MEMBER_CHECK(SFXStateContainer, m_unkDouble1, 0x0);
GEODE_MEMBER_CHECK(SFXStateContainer, m_unkDouble2, 0x8);
GEODE_MEMBER_CHECK(SFXStateContainer, m_unkFloat1, 0x10);
GEODE_MEMBER_CHECK(SFXStateContainer, m_unkFloat2, 0x14);
GEODE_MEMBER_CHECK(SFXStateContainer, m_unkBool, 0x18);

GEODE_SIZE_CHECK(SFXTriggerState, 0xa0);
GEODE_MEMBER_CHECK(SFXTriggerState, m_sfxTriggerGameObject, 0x0);
GEODE_MEMBER_CHECK(SFXTriggerState, m_unkInt1, 0x4);
GEODE_MEMBER_CHECK(SFXTriggerState, m_unkDouble1, 0x8);
GEODE_MEMBER_CHECK(SFXTriggerState, m_unkDouble2, 0x10);
GEODE_MEMBER_CHECK(SFXTriggerState, m_unkDouble3, 0x18);
GEODE_MEMBER_CHECK(SFXTriggerState, m_unkDouble4, 0x20);
GEODE_MEMBER_CHECK(SFXTriggerState, m_unkFloat1, 0x28);
GEODE_MEMBER_CHECK(SFXTriggerState, m_unkFloat2, 0x2c);
GEODE_MEMBER_CHECK(SFXTriggerState, m_unkFloat3, 0x30);
GEODE_MEMBER_CHECK(SFXTriggerState, m_unkFloat4, 0x34);
GEODE_MEMBER_CHECK(SFXTriggerState, m_unkInt2, 0x38);
GEODE_MEMBER_CHECK(SFXTriggerState, m_processed, 0x3c);
GEODE_MEMBER_CHECK(SFXTriggerState, m_unkBool1, 0x3d);
GEODE_MEMBER_CHECK(SFXTriggerState, m_sfxStateContainers, 0x40);

GEODE_SIZE_CHECK(GJGameState, 0x4b8);
GEODE_MEMBER_CHECK(GJGameState, m_cameraZoom, 0x0);
GEODE_MEMBER_CHECK(GJGameState, m_targetCameraZoom, 0x4);
GEODE_MEMBER_CHECK(GJGameState, m_cameraOffset, 0x8);
GEODE_MEMBER_CHECK(GJGameState, m_unkPoint1, 0x10);
GEODE_MEMBER_CHECK(GJGameState, m_unkPoint2, 0x18);
GEODE_MEMBER_CHECK(GJGameState, m_unkPoint3, 0x20);
GEODE_MEMBER_CHECK(GJGameState, m_unkPoint4, 0x28);
GEODE_MEMBER_CHECK(GJGameState, m_unkPoint5, 0x30);
GEODE_MEMBER_CHECK(GJGameState, m_unkPoint6, 0x38);
GEODE_MEMBER_CHECK(GJGameState, m_unkPoint7, 0x40);
GEODE_MEMBER_CHECK(GJGameState, m_unkPoint8, 0x48);
GEODE_MEMBER_CHECK(GJGameState, m_unkPoint9, 0x50);
GEODE_MEMBER_CHECK(GJGameState, m_unkPoint10, 0x58);
GEODE_MEMBER_CHECK(GJGameState, m_unkPoint11, 0x60);
GEODE_MEMBER_CHECK(GJGameState, m_unkPoint12, 0x68);
GEODE_MEMBER_CHECK(GJGameState, m_unkPoint13, 0x70);
GEODE_MEMBER_CHECK(GJGameState, m_unkPoint14, 0x78);
GEODE_MEMBER_CHECK(GJGameState, m_unkPoint15, 0x80);
GEODE_MEMBER_CHECK(GJGameState, m_unkPoint16, 0x88);
GEODE_MEMBER_CHECK(GJGameState, m_unkPoint17, 0x90);
GEODE_MEMBER_CHECK(GJGameState, m_unkPoint18, 0x98);
GEODE_MEMBER_CHECK(GJGameState, m_unkPoint19, 0xa0);
GEODE_MEMBER_CHECK(GJGameState, m_unkPoint20, 0xa8);
GEODE_MEMBER_CHECK(GJGameState, m_unkPoint21, 0xb0);
GEODE_MEMBER_CHECK(GJGameState, m_unkPoint22, 0xb8);
GEODE_MEMBER_CHECK(GJGameState, m_unkPoint23, 0xc0);
GEODE_MEMBER_CHECK(GJGameState, m_unkPoint24, 0xc8);
GEODE_MEMBER_CHECK(GJGameState, m_unkPoint25, 0xd0);
GEODE_MEMBER_CHECK(GJGameState, m_unkPoint26, 0xd8);
GEODE_MEMBER_CHECK(GJGameState, m_unkPoint27, 0xe0);
GEODE_MEMBER_CHECK(GJGameState, m_unkPoint28, 0xe8);
GEODE_MEMBER_CHECK(GJGameState, m_unkPoint29, 0xf0);
GEODE_MEMBER_CHECK(GJGameState, m_unkBool1, 0xf8);
GEODE_MEMBER_CHECK(GJGameState, m_unkInt1, 0xfc);
GEODE_MEMBER_CHECK(GJGameState, m_unkBool2, 0x100);
GEODE_MEMBER_CHECK(GJGameState, m_unkInt2, 0x104);
GEODE_MEMBER_CHECK(GJGameState, m_unkBool3, 0x108);
GEODE_MEMBER_CHECK(GJGameState, m_unkPoint30, 0x10c);
GEODE_MEMBER_CHECK(GJGameState, m_middleGroundOffsetY, 0x114);
GEODE_MEMBER_CHECK(GJGameState, m_unkInt3, 0x118);
GEODE_MEMBER_CHECK(GJGameState, m_unkInt4, 0x11c);
GEODE_MEMBER_CHECK(GJGameState, m_unkBool4, 0x120);
GEODE_MEMBER_CHECK(GJGameState, m_unkBool5, 0x121);
GEODE_MEMBER_CHECK(GJGameState, m_unkFloat2, 0x124);
GEODE_MEMBER_CHECK(GJGameState, m_unkFloat3, 0x128);
GEODE_MEMBER_CHECK(GJGameState, m_unkInt5, 0x12c);
GEODE_MEMBER_CHECK(GJGameState, m_unkInt6, 0x130);
GEODE_MEMBER_CHECK(GJGameState, m_unkInt7, 0x134);
GEODE_MEMBER_CHECK(GJGameState, m_unkInt8, 0x138);
GEODE_MEMBER_CHECK(GJGameState, m_unkInt9, 0x13c);
GEODE_MEMBER_CHECK(GJGameState, m_unkInt10, 0x140);
GEODE_MEMBER_CHECK(GJGameState, m_unkInt11, 0x144);
GEODE_MEMBER_CHECK(GJGameState, m_unkFloat4, 0x148);
GEODE_MEMBER_CHECK(GJGameState, m_unkUint1, 0x14c);
GEODE_MEMBER_CHECK(GJGameState, m_portalY, 0x150);
GEODE_MEMBER_CHECK(GJGameState, m_unkBool6, 0x154);
GEODE_MEMBER_CHECK(GJGameState, m_gravityRelated, 0x155);
GEODE_MEMBER_CHECK(GJGameState, m_unkInt12, 0x158);
GEODE_MEMBER_CHECK(GJGameState, m_unkInt13, 0x15c);
GEODE_MEMBER_CHECK(GJGameState, m_unkInt14, 0x160);
GEODE_MEMBER_CHECK(GJGameState, m_unkInt15, 0x164);
GEODE_MEMBER_CHECK(GJGameState, m_unkBool7, 0x168);
GEODE_MEMBER_CHECK(GJGameState, m_unkBool8, 0x169);
GEODE_MEMBER_CHECK(GJGameState, m_unkBool9, 0x16a);
GEODE_MEMBER_CHECK(GJGameState, m_unkFloat5, 0x16c);
GEODE_MEMBER_CHECK(GJGameState, m_unkFloat6, 0x170);
GEODE_MEMBER_CHECK(GJGameState, m_unkFloat7, 0x174);
GEODE_MEMBER_CHECK(GJGameState, m_unkFloat8, 0x178);
GEODE_MEMBER_CHECK(GJGameState, m_cameraAngle, 0x17c);
GEODE_MEMBER_CHECK(GJGameState, m_targetCameraAngle, 0x180);
GEODE_MEMBER_CHECK(GJGameState, m_playerStreakBlend, 0x184);
GEODE_MEMBER_CHECK(GJGameState, m_timeWarp, 0x188);
GEODE_MEMBER_CHECK(GJGameState, m_timeWarpRelated, 0x18c);
GEODE_MEMBER_CHECK(GJGameState, m_currentChannel, 0x190);
GEODE_MEMBER_CHECK(GJGameState, m_rotateChannel, 0x194);
GEODE_MEMBER_CHECK(GJGameState, m_spawnChannelRelated0, 0x198);
GEODE_MEMBER_CHECK(GJGameState, m_spawnChannelRelated1, 0x1b4);
GEODE_MEMBER_CHECK(GJGameState, m_totalTime, 0x1d0);
GEODE_MEMBER_CHECK(GJGameState, m_levelTime, 0x1d8);
GEODE_MEMBER_CHECK(GJGameState, m_unkDouble3, 0x1e0);
GEODE_MEMBER_CHECK(GJGameState, m_unkUint2, 0x1e8);
GEODE_MEMBER_CHECK(GJGameState, m_unkUint3, 0x1ec);
GEODE_MEMBER_CHECK(GJGameState, m_currentProgress, 0x1f0);
GEODE_MEMBER_CHECK(GJGameState, m_unkUint4, 0x1f4);
GEODE_MEMBER_CHECK(GJGameState, m_unkUint5, 0x1f8);
GEODE_MEMBER_CHECK(GJGameState, m_unkUint6, 0x1fc);
GEODE_MEMBER_CHECK(GJGameState, m_unkUint7, 0x200);
GEODE_MEMBER_CHECK(GJGameState, m_unkGameObjPtr1, 0x204);
GEODE_MEMBER_CHECK(GJGameState, m_unkGameObjPtr2, 0x208);
GEODE_MEMBER_CHECK(GJGameState, m_cameraPosition, 0x20c);
GEODE_MEMBER_CHECK(GJGameState, m_unkBool10, 0x214);
GEODE_MEMBER_CHECK(GJGameState, m_levelFlipping, 0x218);
GEODE_MEMBER_CHECK(GJGameState, m_unkBool11, 0x21c);
GEODE_MEMBER_CHECK(GJGameState, m_unkBool12, 0x21d);
GEODE_MEMBER_CHECK(GJGameState, m_isDualMode, 0x21e);
GEODE_MEMBER_CHECK(GJGameState, m_unkFloat9, 0x220);
GEODE_MEMBER_CHECK(GJGameState, m_tweenActions, 0x224);
GEODE_MEMBER_CHECK(GJGameState, m_cameraEdgeValue0, 0x240);
GEODE_MEMBER_CHECK(GJGameState, m_cameraEdgeValue1, 0x244);
GEODE_MEMBER_CHECK(GJGameState, m_cameraEdgeValue2, 0x248);
GEODE_MEMBER_CHECK(GJGameState, m_cameraEdgeValue3, 0x24c);
GEODE_MEMBER_CHECK(GJGameState, m_gameObjectPhysics, 0x250);
GEODE_MEMBER_CHECK(GJGameState, m_unkVecFloat1, 0x26c);
GEODE_MEMBER_CHECK(GJGameState, m_unkUint10, 0x278);
GEODE_MEMBER_CHECK(GJGameState, m_unkUint11, 0x27c);
GEODE_MEMBER_CHECK(GJGameState, m_unkUint12, 0x280);
GEODE_MEMBER_CHECK(GJGameState, m_cameraStepDiff, 0x284);
GEODE_MEMBER_CHECK(GJGameState, m_unkFloat10, 0x28c);
GEODE_MEMBER_CHECK(GJGameState, m_timeModRelated, 0x290);
GEODE_MEMBER_CHECK(GJGameState, m_timeModRelated2, 0x294);
GEODE_MEMBER_CHECK(GJGameState, m_unkMapPairIntIntInt, 0x298);
GEODE_MEMBER_CHECK(GJGameState, m_unkUint13, 0x2b0);
GEODE_MEMBER_CHECK(GJGameState, m_unkPoint32, 0x2b4);
GEODE_MEMBER_CHECK(GJGameState, m_unkPoint33, 0x2bc);
GEODE_MEMBER_CHECK(GJGameState, m_unkBool20, 0x2c4);
GEODE_MEMBER_CHECK(GJGameState, m_unkBool21, 0x2c5);
GEODE_MEMBER_CHECK(GJGameState, m_unkBool22, 0x2c6);
GEODE_MEMBER_CHECK(GJGameState, m_unkUint14, 0x2c8);
GEODE_MEMBER_CHECK(GJGameState, m_unkBool26, 0x2cc);
GEODE_MEMBER_CHECK(GJGameState, m_cameraShakeEnabled, 0x2cd);
GEODE_MEMBER_CHECK(GJGameState, m_cameraShakeFactor, 0x2d0);
GEODE_MEMBER_CHECK(GJGameState, m_unkUint15, 0x2d4);
GEODE_MEMBER_CHECK(GJGameState, m_unkUint16, 0x2d8);
GEODE_MEMBER_CHECK(GJGameState, m_unkUint64_1, 0x2e0);
GEODE_MEMBER_CHECK(GJGameState, m_unkPoint34, 0x2e8);
GEODE_MEMBER_CHECK(GJGameState, m_dualRelated, 0x2f0);
GEODE_MEMBER_CHECK(GJGameState, m_stateObjects, 0x2f4);
GEODE_MEMBER_CHECK(GJGameState, m_unkMapPairGJGameEventIntVectorEventTriggerInstance, 0x310);
GEODE_MEMBER_CHECK(GJGameState, m_unkMapPairGJGameEventIntInt, 0x328);
GEODE_MEMBER_CHECK(GJGameState, m_unorderedMapEnterEffectInstanceVectors1, 0x340);
GEODE_MEMBER_CHECK(GJGameState, m_unorderedMapEnterEffectInstanceVectors2, 0x35c);
GEODE_MEMBER_CHECK(GJGameState, m_unkVecInt1, 0x378);
GEODE_MEMBER_CHECK(GJGameState, m_unkVecInt2, 0x384);
GEODE_MEMBER_CHECK(GJGameState, m_enterEffectInstances1, 0x390);
GEODE_MEMBER_CHECK(GJGameState, m_enterEffectInstances2, 0x39c);
GEODE_MEMBER_CHECK(GJGameState, m_enterEffectInstances3, 0x3a8);
GEODE_MEMBER_CHECK(GJGameState, m_enterEffectInstances4, 0x3b4);
GEODE_MEMBER_CHECK(GJGameState, m_enterEffectInstances5, 0x3c0);
GEODE_MEMBER_CHECK(GJGameState, m_unkUnorderedSet1, 0x3cc);
GEODE_MEMBER_CHECK(GJGameState, m_unkBool27, 0x3e8);
GEODE_MEMBER_CHECK(GJGameState, m_advanceFollowInstances, 0x3ec);
GEODE_MEMBER_CHECK(GJGameState, m_dynamicObjActions1, 0x3f8);
GEODE_MEMBER_CHECK(GJGameState, m_dynamicObjActions2, 0x404);
GEODE_MEMBER_CHECK(GJGameState, m_unkBool28, 0x410);
GEODE_MEMBER_CHECK(GJGameState, m_unkBool29, 0x411);
GEODE_MEMBER_CHECK(GJGameState, m_unkUint17, 0x414);
GEODE_MEMBER_CHECK(GJGameState, m_unkUMap8, 0x418);
GEODE_MEMBER_CHECK(GJGameState, m_proximityVolumeRelated, 0x434);
GEODE_MEMBER_CHECK(GJGameState, m_songChannelStates, 0x44c);
GEODE_MEMBER_CHECK(GJGameState, m_songTriggerStateVectors, 0x468);
GEODE_MEMBER_CHECK(GJGameState, m_sfxTriggerStates, 0x484);
GEODE_MEMBER_CHECK(GJGameState, m_unkBool30, 0x490);
GEODE_MEMBER_CHECK(GJGameState, m_background, 0x494);
GEODE_MEMBER_CHECK(GJGameState, m_ground, 0x498);
GEODE_MEMBER_CHECK(GJGameState, m_middleground, 0x49c);
GEODE_MEMBER_CHECK(GJGameState, m_unkBool31, 0x4a0);
GEODE_MEMBER_CHECK(GJGameState, m_points, 0x4a4);
GEODE_MEMBER_CHECK(GJGameState, m_unkBool32, 0x4a8);
GEODE_MEMBER_CHECK(GJGameState, m_pauseCounter, 0x4ac);
GEODE_MEMBER_CHECK(GJGameState, m_pauseBufferTimer, 0x4b0);

GEODE_SIZE_CHECK(GJShaderState, 0x280);
GEODE_MEMBER_CHECK(GJShaderState, m_someIntToValueTweenMap, 0x0);
GEODE_MEMBER_CHECK(GJShaderState, m_someIntToDoubleMap, 0x1c);
GEODE_MEMBER_CHECK(GJShaderState, m_time, 0x38);
GEODE_MEMBER_CHECK(GJShaderState, m_prevTime, 0x40);
GEODE_MEMBER_CHECK(GJShaderState, m_startTime, 0x48);
GEODE_MEMBER_CHECK(GJShaderState, m_textureScaleX, 0x50);
GEODE_MEMBER_CHECK(GJShaderState, m_textureScaleY, 0x54);
GEODE_MEMBER_CHECK(GJShaderState, m_blurRefColor, 0x58);
GEODE_MEMBER_CHECK(GJShaderState, m_blurIntensity, 0x5c);
GEODE_MEMBER_CHECK(GJShaderState, m_blurUnk60, 0x60);
GEODE_MEMBER_CHECK(GJShaderState, m_blurOnlyEmpty, 0x64);
GEODE_MEMBER_CHECK(GJShaderState, m_shockWaveUnk68, 0x68);
GEODE_MEMBER_CHECK(GJShaderState, m_shockWaveUnk6c, 0x6c);
GEODE_MEMBER_CHECK(GJShaderState, m_shockWaveUnk70, 0x70);
GEODE_MEMBER_CHECK(GJShaderState, m_shockWaveUnk74, 0x74);
GEODE_MEMBER_CHECK(GJShaderState, m_shockWaveUnk78, 0x78);
GEODE_MEMBER_CHECK(GJShaderState, m_shockWaveUnk7c, 0x7c);
GEODE_MEMBER_CHECK(GJShaderState, m_shockWaveUnk80, 0x80);
GEODE_MEMBER_CHECK(GJShaderState, m_shockWaveUnk84, 0x84);
GEODE_MEMBER_CHECK(GJShaderState, m_shockWaveUnk88, 0x88);
GEODE_MEMBER_CHECK(GJShaderState, m_shockWaveInvert, 0x8c);
GEODE_MEMBER_CHECK(GJShaderState, m_shockWaveUnk90, 0x90);
GEODE_MEMBER_CHECK(GJShaderState, m_shockWaveUnk94, 0x94);
GEODE_MEMBER_CHECK(GJShaderState, m_shockWaveUnk98, 0x98);
GEODE_MEMBER_CHECK(GJShaderState, m_shockWaveUnk9c, 0x9c);
GEODE_MEMBER_CHECK(GJShaderState, m_shockWaveCenterMoving, 0x9d);
GEODE_MEMBER_CHECK(GJShaderState, m_shockWaveUnk9e, 0x9e);
GEODE_MEMBER_CHECK(GJShaderState, m_shockWaveUnka0, 0xa0);
GEODE_MEMBER_CHECK(GJShaderState, m_shockWaveUnka4, 0xa4);
GEODE_MEMBER_CHECK(GJShaderState, m_shockWaveCenterDirty, 0xac);
GEODE_MEMBER_CHECK(GJShaderState, m_shockWaveCenter, 0xb0);
GEODE_MEMBER_CHECK(GJShaderState, m_shockLineUnkb8, 0xb8);
GEODE_MEMBER_CHECK(GJShaderState, m_shockLineUnkc0, 0xc0);
GEODE_MEMBER_CHECK(GJShaderState, m_shockLineUnkc4, 0xc4);
GEODE_MEMBER_CHECK(GJShaderState, m_shockLineUnkc8, 0xc8);
GEODE_MEMBER_CHECK(GJShaderState, m_shockLineAxis, 0xcc);
GEODE_MEMBER_CHECK(GJShaderState, m_shockLineDirection, 0xcd);
GEODE_MEMBER_CHECK(GJShaderState, m_shockLineDual, 0xce);
GEODE_MEMBER_CHECK(GJShaderState, m_shockLineUnkcf, 0xcf);
GEODE_MEMBER_CHECK(GJShaderState, m_shockLineUnkd0, 0xd0);
GEODE_MEMBER_CHECK(GJShaderState, m_shockLineUnkd4, 0xd4);
GEODE_MEMBER_CHECK(GJShaderState, m_shockLineUnkd8, 0xd8);
GEODE_MEMBER_CHECK(GJShaderState, m_shockLineUnkdc, 0xdc);
GEODE_MEMBER_CHECK(GJShaderState, m_shockLineUnke0, 0xe0);
GEODE_MEMBER_CHECK(GJShaderState, m_shockLineUnke4, 0xe4);
GEODE_MEMBER_CHECK(GJShaderState, m_shockLineUnke8, 0xe8);
GEODE_MEMBER_CHECK(GJShaderState, m_shockLineUnkec, 0xec);
GEODE_MEMBER_CHECK(GJShaderState, m_shockLineCenterMoving, 0xed);
GEODE_MEMBER_CHECK(GJShaderState, m_shockLineUnkee, 0xee);
GEODE_MEMBER_CHECK(GJShaderState, m_shockLineUnkf0, 0xf0);
GEODE_MEMBER_CHECK(GJShaderState, m_shockLineUnkf4, 0xf4);
GEODE_MEMBER_CHECK(GJShaderState, m_shockLineCenterDirty, 0xfc);
GEODE_MEMBER_CHECK(GJShaderState, m_shockLineCenter, 0x100);
GEODE_MEMBER_CHECK(GJShaderState, m_glitchUnk108, 0x108);
GEODE_MEMBER_CHECK(GJShaderState, m_glitchUnk10c, 0x10c);
GEODE_MEMBER_CHECK(GJShaderState, m_glitchUnk110, 0x110);
GEODE_MEMBER_CHECK(GJShaderState, m_glitchUnk114, 0x114);
GEODE_MEMBER_CHECK(GJShaderState, m_glitchUnk118, 0x118);
GEODE_MEMBER_CHECK(GJShaderState, m_glitchUnk11c, 0x11c);
GEODE_MEMBER_CHECK(GJShaderState, m_glitchUnk120, 0x120);
GEODE_MEMBER_CHECK(GJShaderState, m_chromaticUnk124, 0x124);
GEODE_MEMBER_CHECK(GJShaderState, m_chromaticUnk128, 0x128);
GEODE_MEMBER_CHECK(GJShaderState, m_chromaticUnk12c, 0x12c);
GEODE_MEMBER_CHECK(GJShaderState, m_cGUnk130, 0x130);
GEODE_MEMBER_CHECK(GJShaderState, m_cGUnk134, 0x134);
GEODE_MEMBER_CHECK(GJShaderState, m_cGUnk138, 0x138);
GEODE_MEMBER_CHECK(GJShaderState, m_cGUnk13c, 0x13c);
GEODE_MEMBER_CHECK(GJShaderState, m_cGUnk140, 0x140);
GEODE_MEMBER_CHECK(GJShaderState, m_cGUnk144, 0x144);
GEODE_MEMBER_CHECK(GJShaderState, m_cGUnk148, 0x148);
GEODE_MEMBER_CHECK(GJShaderState, m_cGUnk14c, 0x14c);
GEODE_MEMBER_CHECK(GJShaderState, m_cGUnk150, 0x150);
GEODE_MEMBER_CHECK(GJShaderState, m_cGUnk151, 0x151);
GEODE_MEMBER_CHECK(GJShaderState, m_cGUnk152, 0x152);
GEODE_MEMBER_CHECK(GJShaderState, m_cGUnk153, 0x153);
GEODE_MEMBER_CHECK(GJShaderState, m_pixelateTargetX, 0x154);
GEODE_MEMBER_CHECK(GJShaderState, m_pixelateTargetY, 0x158);
GEODE_MEMBER_CHECK(GJShaderState, m_pixelateSnapGrid, 0x15c);
GEODE_MEMBER_CHECK(GJShaderState, m_pixelatePixelating, 0x15d);
GEODE_MEMBER_CHECK(GJShaderState, m_pixelateRelative, 0x15e);
GEODE_MEMBER_CHECK(GJShaderState, m_pixelateHardEdges, 0x15f);
GEODE_MEMBER_CHECK(GJShaderState, m_lensCircleUnk160, 0x160);
GEODE_MEMBER_CHECK(GJShaderState, m_lensCircleUnk164, 0x164);
GEODE_MEMBER_CHECK(GJShaderState, m_lensCircleStrength, 0x168);
GEODE_MEMBER_CHECK(GJShaderState, m_lensCircleUnk16c, 0x16c);
GEODE_MEMBER_CHECK(GJShaderState, m_lensCircleUnk170, 0x170);
GEODE_MEMBER_CHECK(GJShaderState, m_lensCircleUnk174, 0x174);
GEODE_MEMBER_CHECK(GJShaderState, m_lensCircleAdditive, 0x175);
GEODE_MEMBER_CHECK(GJShaderState, m_lensCircleTintR, 0x176);
GEODE_MEMBER_CHECK(GJShaderState, m_lensCircleTintG, 0x177);
GEODE_MEMBER_CHECK(GJShaderState, m_lensCircleTintB, 0x178);
GEODE_MEMBER_CHECK(GJShaderState, m_lensCircleUnk17c, 0x17c);
GEODE_MEMBER_CHECK(GJShaderState, m_lensCircleUnk184, 0x184);
GEODE_MEMBER_CHECK(GJShaderState, m_radialBlurUnk18c, 0x18c);
GEODE_MEMBER_CHECK(GJShaderState, m_radialBlurUnk190, 0x190);
GEODE_MEMBER_CHECK(GJShaderState, m_radialBlurUnk194, 0x194);
GEODE_MEMBER_CHECK(GJShaderState, m_radialBlurUnk198, 0x198);
GEODE_MEMBER_CHECK(GJShaderState, m_radialBlurUnk19c, 0x19c);
GEODE_MEMBER_CHECK(GJShaderState, m_radialBlurUnk1a4, 0x1a4);
GEODE_MEMBER_CHECK(GJShaderState, m_motionBlurUnk1ac, 0x1ac);
GEODE_MEMBER_CHECK(GJShaderState, m_motionBlurUnk1b0, 0x1b0);
GEODE_MEMBER_CHECK(GJShaderState, m_motionBlurSpeedX, 0x1b4);
GEODE_MEMBER_CHECK(GJShaderState, m_motionBlurSpeedY, 0x1b8);
GEODE_MEMBER_CHECK(GJShaderState, m_motionBlurUnk1bc, 0x1bc);
GEODE_MEMBER_CHECK(GJShaderState, m_motionBlurUnk1c0, 0x1c0);
GEODE_MEMBER_CHECK(GJShaderState, m_motionBlurUnk1c4, 0x1c4);
GEODE_MEMBER_CHECK(GJShaderState, m_motionBlurUnk1c8, 0x1c8);
GEODE_MEMBER_CHECK(GJShaderState, m_motionBlurDual, 0x1cc);
GEODE_MEMBER_CHECK(GJShaderState, m_motionBlurUnk1cd, 0x1cd);
GEODE_MEMBER_CHECK(GJShaderState, m_bulgeValue, 0x1d0);
GEODE_MEMBER_CHECK(GJShaderState, m_bulgeUnk1d4, 0x1d4);
GEODE_MEMBER_CHECK(GJShaderState, m_bulgeUnk1d8, 0x1d8);
GEODE_MEMBER_CHECK(GJShaderState, m_bulgeRadius, 0x1dc);
GEODE_MEMBER_CHECK(GJShaderState, m_bulgeUnk1e0, 0x1e0);
GEODE_MEMBER_CHECK(GJShaderState, m_bulgeUnk1e8, 0x1e8);
GEODE_MEMBER_CHECK(GJShaderState, m_pinchUnk1f0, 0x1f0);
GEODE_MEMBER_CHECK(GJShaderState, m_pinchUnk1f4, 0x1f4);
GEODE_MEMBER_CHECK(GJShaderState, m_pinchUnk1f8, 0x1f8);
GEODE_MEMBER_CHECK(GJShaderState, m_pinchUnk1f9, 0x1f9);
GEODE_MEMBER_CHECK(GJShaderState, m_pinchUnk1fc, 0x1fc);
GEODE_MEMBER_CHECK(GJShaderState, m_pinchUnk200, 0x200);
GEODE_MEMBER_CHECK(GJShaderState, m_pinchUnk204, 0x204);
GEODE_MEMBER_CHECK(GJShaderState, m_pinchUnk20c, 0x20c);
GEODE_MEMBER_CHECK(GJShaderState, m_pinchUnk214, 0x214);
GEODE_MEMBER_CHECK(GJShaderState, m_pinchUnk218, 0x218);
GEODE_MEMBER_CHECK(GJShaderState, m_pinchUnk21c, 0x21c);
GEODE_MEMBER_CHECK(GJShaderState, m_grayscaleValue, 0x224);
GEODE_MEMBER_CHECK(GJShaderState, m_grayscaleUseLum, 0x228);
GEODE_MEMBER_CHECK(GJShaderState, m_grayscaleUnk22c, 0x22c);
GEODE_MEMBER_CHECK(GJShaderState, m_grayscaleTint, 0x230);
GEODE_MEMBER_CHECK(GJShaderState, m_sepiaValue, 0x234);
GEODE_MEMBER_CHECK(GJShaderState, m_invertColorEditRGB, 0x238);
GEODE_MEMBER_CHECK(GJShaderState, m_invertColorR, 0x23c);
GEODE_MEMBER_CHECK(GJShaderState, m_invertColorG, 0x240);
GEODE_MEMBER_CHECK(GJShaderState, m_invertColorB, 0x244);
GEODE_MEMBER_CHECK(GJShaderState, m_invertColorClampRGB, 0x248);
GEODE_MEMBER_CHECK(GJShaderState, m_hueShiftDegrees, 0x24c);
GEODE_MEMBER_CHECK(GJShaderState, m_colorChangeCR, 0x250);
GEODE_MEMBER_CHECK(GJShaderState, m_colorChangeCG, 0x254);
GEODE_MEMBER_CHECK(GJShaderState, m_colorChangeCB, 0x258);
GEODE_MEMBER_CHECK(GJShaderState, m_colorChangeBR, 0x25c);
GEODE_MEMBER_CHECK(GJShaderState, m_colorChangeBG, 0x260);
GEODE_MEMBER_CHECK(GJShaderState, m_colorChangeBB, 0x264);
GEODE_MEMBER_CHECK(GJShaderState, m_splitUnk268, 0x268);
GEODE_MEMBER_CHECK(GJShaderState, m_splitUnk26c, 0x26c);
GEODE_MEMBER_CHECK(GJShaderState, m_splitUnk270, 0x270);
GEODE_MEMBER_CHECK(GJShaderState, m_minBlendingLayer, 0x274);
GEODE_MEMBER_CHECK(GJShaderState, m_maxBlendingLayer, 0x278);
GEODE_MEMBER_CHECK(GJShaderState, m_zLayerDirty, 0x27c);
GEODE_MEMBER_CHECK(GJShaderState, m_somethingZLayerUnk27d, 0x27d);
GEODE_MEMBER_CHECK(GJShaderState, m_usesShaders, 0x27e);

GEODE_SIZE_CHECK(FMODSoundTween, 0x18);
GEODE_MEMBER_CHECK(FMODSoundTween, m_interval, 0x0);
GEODE_MEMBER_CHECK(FMODSoundTween, m_duration, 0x4);
GEODE_MEMBER_CHECK(FMODSoundTween, m_start, 0x8);
GEODE_MEMBER_CHECK(FMODSoundTween, m_end, 0xc);
GEODE_MEMBER_CHECK(FMODSoundTween, m_value, 0x10);
GEODE_MEMBER_CHECK(FMODSoundTween, m_finished, 0x14);

GEODE_SIZE_CHECK(FMODQueuedMusic, 0x40);
GEODE_MEMBER_CHECK(FMODQueuedMusic, m_filePath, 0x0);
GEODE_MEMBER_CHECK(FMODQueuedMusic, m_pitch, 0x4);
GEODE_MEMBER_CHECK(FMODQueuedMusic, m_unkFloat2, 0x8);
GEODE_MEMBER_CHECK(FMODQueuedMusic, m_volume, 0xc);
GEODE_MEMBER_CHECK(FMODQueuedMusic, m_start, 0x10);
GEODE_MEMBER_CHECK(FMODQueuedMusic, m_end, 0x14);
GEODE_MEMBER_CHECK(FMODQueuedMusic, m_fadeIn, 0x18);
GEODE_MEMBER_CHECK(FMODQueuedMusic, m_fadeOut, 0x1c);
GEODE_MEMBER_CHECK(FMODQueuedMusic, m_loop, 0x20);
GEODE_MEMBER_CHECK(FMODQueuedMusic, m_musicID, 0x24);
GEODE_MEMBER_CHECK(FMODQueuedMusic, m_stopMusic, 0x28);
GEODE_MEMBER_CHECK(FMODQueuedMusic, m_channelID, 0x2c);
GEODE_MEMBER_CHECK(FMODQueuedMusic, m_noPrepare, 0x30);
GEODE_MEMBER_CHECK(FMODQueuedMusic, m_queuedStatus, 0x34);
GEODE_MEMBER_CHECK(FMODQueuedMusic, m_sound, 0x38);
GEODE_MEMBER_CHECK(FMODQueuedMusic, m_dontReset, 0x3c);

GEODE_SIZE_CHECK(SoundStateContainer, 0x68);
GEODE_MEMBER_CHECK(SoundStateContainer, m_fadePointCount, 0x0);
GEODE_MEMBER_CHECK(SoundStateContainer, m_fadePointVolumes, 0x4);
GEODE_MEMBER_CHECK(SoundStateContainer, m_fadePointOffsets, 0x18);
GEODE_MEMBER_CHECK(SoundStateContainer, m_currentOffset, 0x38);
GEODE_MEMBER_CHECK(SoundStateContainer, m_loopStartMs, 0x40);
GEODE_MEMBER_CHECK(SoundStateContainer, m_loopEndMs, 0x44);
GEODE_MEMBER_CHECK(SoundStateContainer, m_currentMs, 0x48);
GEODE_MEMBER_CHECK(SoundStateContainer, m_playStartOffset, 0x50);
GEODE_MEMBER_CHECK(SoundStateContainer, m_playEndOffset, 0x58);
GEODE_MEMBER_CHECK(SoundStateContainer, m_usePlayOffsets, 0x60);

GEODE_SIZE_CHECK(FMODSoundState, 0xa0);
GEODE_MEMBER_CHECK(FMODSoundState, m_filePath, 0x0);
GEODE_MEMBER_CHECK(FMODSoundState, m_speed, 0x4);
GEODE_MEMBER_CHECK(FMODSoundState, m_unkFloat1, 0x8);
GEODE_MEMBER_CHECK(FMODSoundState, m_volume, 0xc);
GEODE_MEMBER_CHECK(FMODSoundState, m_shouldLoop, 0x10);
GEODE_MEMBER_CHECK(FMODSoundState, m_channelID, 0x14);
GEODE_MEMBER_CHECK(FMODSoundState, m_soundStateContainer, 0x18);
GEODE_MEMBER_CHECK(FMODSoundState, m_uniqueID, 0x80);
GEODE_MEMBER_CHECK(FMODSoundState, m_sfxGroup, 0x84);
GEODE_MEMBER_CHECK(FMODSoundState, m_pitch, 0x88);
GEODE_MEMBER_CHECK(FMODSoundState, m_fastFourierTransform, 0x8c);
GEODE_MEMBER_CHECK(FMODSoundState, m_reverb, 0x8d);
GEODE_MEMBER_CHECK(FMODSoundState, m_effectID, 0x90);
GEODE_MEMBER_CHECK(FMODSoundState, m_isMusic, 0x94);
GEODE_MEMBER_CHECK(FMODSoundState, m_musicID, 0x98);
GEODE_MEMBER_CHECK(FMODSoundState, m_unkBool2, 0x9c);

GEODE_SIZE_CHECK(FMODAudioState, 0x1c8);
GEODE_MEMBER_CHECK(FMODAudioState, m_interval, 0x0);
GEODE_MEMBER_CHECK(FMODAudioState, m_elapsed, 0x4);
GEODE_MEMBER_CHECK(FMODAudioState, m_tweensForEffectChannels, 0x8);
GEODE_MEMBER_CHECK(FMODAudioState, m_tweensForEffectGroups, 0x20);
GEODE_MEMBER_CHECK(FMODAudioState, m_tweensForMusicChannels, 0x38);
GEODE_MEMBER_CHECK(FMODAudioState, m_volumeForEffectChannels, 0x50);
GEODE_MEMBER_CHECK(FMODAudioState, m_volumeModForEffectChannels, 0x6c);
GEODE_MEMBER_CHECK(FMODAudioState, m_pitchForEffectChannels, 0x88);
GEODE_MEMBER_CHECK(FMODAudioState, m_volumeForEffectGroups, 0xa4);
GEODE_MEMBER_CHECK(FMODAudioState, m_volumeModForEffectGroups, 0xc0);
GEODE_MEMBER_CHECK(FMODAudioState, m_pitchForEffectGroups, 0xdc);
GEODE_MEMBER_CHECK(FMODAudioState, m_volumeForMusicChannels, 0xf8);
GEODE_MEMBER_CHECK(FMODAudioState, m_volumeModForMusicChannels, 0x114);
GEODE_MEMBER_CHECK(FMODAudioState, m_pitchForMusicChannels, 0x130);
GEODE_MEMBER_CHECK(FMODAudioState, m_intervalForEffects, 0x14c);
GEODE_MEMBER_CHECK(FMODAudioState, m_queuedMusicForChannels1, 0x168);
GEODE_MEMBER_CHECK(FMODAudioState, m_queuedMusicForChannels2, 0x184);
GEODE_MEMBER_CHECK(FMODAudioState, m_soundStateForChannels, 0x1a0);
GEODE_MEMBER_CHECK(FMODAudioState, m_unkUint64_1, 0x1c0);

GEODE_SIZE_CHECK(PlayerCheckpoint, 0x180);
GEODE_MEMBER_CHECK(PlayerCheckpoint, m_position, 0x108);
GEODE_MEMBER_CHECK(PlayerCheckpoint, m_lastPosition, 0x110);
GEODE_MEMBER_CHECK(PlayerCheckpoint, m_yVelocity, 0x118);
GEODE_MEMBER_CHECK(PlayerCheckpoint, m_isUpsideDown, 0x11c);
GEODE_MEMBER_CHECK(PlayerCheckpoint, m_isSideways, 0x11d);
GEODE_MEMBER_CHECK(PlayerCheckpoint, m_isShip, 0x11e);
GEODE_MEMBER_CHECK(PlayerCheckpoint, m_isBall, 0x11f);
GEODE_MEMBER_CHECK(PlayerCheckpoint, m_isBird, 0x120);
GEODE_MEMBER_CHECK(PlayerCheckpoint, m_isSwing, 0x121);
GEODE_MEMBER_CHECK(PlayerCheckpoint, m_isDart, 0x122);
GEODE_MEMBER_CHECK(PlayerCheckpoint, m_isRobot, 0x123);
GEODE_MEMBER_CHECK(PlayerCheckpoint, m_isSpider, 0x124);
GEODE_MEMBER_CHECK(PlayerCheckpoint, m_isOnGround, 0x125);
GEODE_MEMBER_CHECK(PlayerCheckpoint, m_ghostType, 0x128);
GEODE_MEMBER_CHECK(PlayerCheckpoint, m_miniMode, 0x12c);
GEODE_MEMBER_CHECK(PlayerCheckpoint, m_speed, 0x130);
GEODE_MEMBER_CHECK(PlayerCheckpoint, m_hidden, 0x134);
GEODE_MEMBER_CHECK(PlayerCheckpoint, m_goingLeft, 0x135);
GEODE_MEMBER_CHECK(PlayerCheckpoint, m_reverseSpeed, 0x138);
GEODE_MEMBER_CHECK(PlayerCheckpoint, m_dashing, 0x13c);
GEODE_MEMBER_CHECK(PlayerCheckpoint, m_dashX, 0x140);
GEODE_MEMBER_CHECK(PlayerCheckpoint, m_dashY, 0x144);
GEODE_MEMBER_CHECK(PlayerCheckpoint, m_dashAngle, 0x148);
GEODE_MEMBER_CHECK(PlayerCheckpoint, m_dashStartTime, 0x14c);
GEODE_MEMBER_CHECK(PlayerCheckpoint, m_dashRingObject, 0x150);
GEODE_MEMBER_CHECK(PlayerCheckpoint, m_platformerCheckpoint, 0x154);
GEODE_MEMBER_CHECK(PlayerCheckpoint, m_lastFlipTime, 0x158);
GEODE_MEMBER_CHECK(PlayerCheckpoint, m_gravityMod, 0x160);
GEODE_MEMBER_CHECK(PlayerCheckpoint, m_decreaseBoostSlide, 0x164);
GEODE_MEMBER_CHECK(PlayerCheckpoint, m_followRelated, 0x168);
GEODE_MEMBER_CHECK(PlayerCheckpoint, m_playerFollowFloats, 0x16c);
GEODE_MEMBER_CHECK(PlayerCheckpoint, m_followRelated2, 0x178);

GEODE_SIZE_CHECK(SavedObjectStateRef, 0x30);
GEODE_MEMBER_CHECK(SavedObjectStateRef, m_gameObject, 0x0);
GEODE_MEMBER_CHECK(SavedObjectStateRef, m_unkDouble1, 0x8);
GEODE_MEMBER_CHECK(SavedObjectStateRef, m_unkDouble2, 0x10);
GEODE_MEMBER_CHECK(SavedObjectStateRef, m_unkFloat1, 0x18);
GEODE_MEMBER_CHECK(SavedObjectStateRef, m_unkFloat2, 0x1c);
GEODE_MEMBER_CHECK(SavedObjectStateRef, m_addToCustomScaleX, 0x20);
GEODE_MEMBER_CHECK(SavedObjectStateRef, m_addToCustomScaleY, 0x24);
GEODE_MEMBER_CHECK(SavedObjectStateRef, m_unkFloat3, 0x28);
GEODE_MEMBER_CHECK(SavedObjectStateRef, m_unkFloat4, 0x2c);

GEODE_SIZE_CHECK(SavedActiveObjectState, 0x8);
GEODE_MEMBER_CHECK(SavedActiveObjectState, m_gameObject, 0x0);
GEODE_MEMBER_CHECK(SavedActiveObjectState, m_unkBool1, 0x4);
GEODE_MEMBER_CHECK(SavedActiveObjectState, m_unkBool2, 0x5);

GEODE_SIZE_CHECK(SavedSpecialObjectState, 0x8);
GEODE_MEMBER_CHECK(SavedSpecialObjectState, m_gameObject, 0x0);
GEODE_MEMBER_CHECK(SavedSpecialObjectState, m_animationID, 0x4);

GEODE_SIZE_CHECK(CAState, 0x44);
GEODE_MEMBER_CHECK(CAState, m_fromColor, 0x0);
GEODE_MEMBER_CHECK(CAState, m_toColor, 0x3);
GEODE_MEMBER_CHECK(CAState, m_color, 0x6);
GEODE_MEMBER_CHECK(CAState, m_paused, 0x9);
GEODE_MEMBER_CHECK(CAState, m_blending, 0xa);
GEODE_MEMBER_CHECK(CAState, m_copyOpacity, 0xb);
GEODE_MEMBER_CHECK(CAState, m_legacyHSV, 0xc);
GEODE_MEMBER_CHECK(CAState, m_playerColor, 0x10);
GEODE_MEMBER_CHECK(CAState, m_colorID, 0x14);
GEODE_MEMBER_CHECK(CAState, m_copyID, 0x18);
GEODE_MEMBER_CHECK(CAState, m_uniqueID, 0x1c);
GEODE_MEMBER_CHECK(CAState, m_duration, 0x20);
GEODE_MEMBER_CHECK(CAState, m_fromOpacity, 0x24);
GEODE_MEMBER_CHECK(CAState, m_toOpacity, 0x28);
GEODE_MEMBER_CHECK(CAState, m_deltaTime, 0x2c);
GEODE_MEMBER_CHECK(CAState, m_currentOpacity, 0x30);
GEODE_MEMBER_CHECK(CAState, m_copyHSV, 0x34);

GEODE_SIZE_CHECK(PulseEffectAction, 0x48);
GEODE_MEMBER_CHECK(PulseEffectAction, m_disabled, 0x0);
GEODE_MEMBER_CHECK(PulseEffectAction, m_fadeInTime, 0x4);
GEODE_MEMBER_CHECK(PulseEffectAction, m_holdTime, 0x8);
GEODE_MEMBER_CHECK(PulseEffectAction, m_fadeOutTime, 0xc);
GEODE_MEMBER_CHECK(PulseEffectAction, m_deltaTime, 0x10);
GEODE_MEMBER_CHECK(PulseEffectAction, m_targetGroupID, 0x14);
GEODE_MEMBER_CHECK(PulseEffectAction, m_currentValue, 0x18);
GEODE_MEMBER_CHECK(PulseEffectAction, m_color, 0x1c);
GEODE_MEMBER_CHECK(PulseEffectAction, m_pulseEffectType, 0x20);
GEODE_MEMBER_CHECK(PulseEffectAction, m_hsv, 0x24);
GEODE_MEMBER_CHECK(PulseEffectAction, m_colorIndex, 0x34);
GEODE_MEMBER_CHECK(PulseEffectAction, m_mainOnly, 0x38);
GEODE_MEMBER_CHECK(PulseEffectAction, m_detailOnly, 0x39);
GEODE_MEMBER_CHECK(PulseEffectAction, m_isDynamicHsv, 0x3a);
GEODE_MEMBER_CHECK(PulseEffectAction, m_triggerUniqueID, 0x3c);
GEODE_MEMBER_CHECK(PulseEffectAction, m_controlID, 0x40);
GEODE_MEMBER_CHECK(PulseEffectAction, m_startTime, 0x44);

GEODE_SIZE_CHECK(CountTriggerAction, 0x30);
GEODE_MEMBER_CHECK(CountTriggerAction, m_disabled, 0x0);
GEODE_MEMBER_CHECK(CountTriggerAction, m_previousCount, 0x4);
GEODE_MEMBER_CHECK(CountTriggerAction, m_targetCount, 0x8);
GEODE_MEMBER_CHECK(CountTriggerAction, m_targetGroupID, 0xc);
GEODE_MEMBER_CHECK(CountTriggerAction, m_activateGroup, 0x10);
GEODE_MEMBER_CHECK(CountTriggerAction, m_triggerUniqueID, 0x14);
GEODE_MEMBER_CHECK(CountTriggerAction, m_controlID, 0x18);
GEODE_MEMBER_CHECK(CountTriggerAction, m_itemID, 0x1c);
GEODE_MEMBER_CHECK(CountTriggerAction, m_multiActivate, 0x20);
GEODE_MEMBER_CHECK(CountTriggerAction, m_remapKeys, 0x24);

GEODE_SIZE_CHECK(OpacityEffectAction, 0x2c);
GEODE_MEMBER_CHECK(OpacityEffectAction, m_duration, 0x0);
GEODE_MEMBER_CHECK(OpacityEffectAction, m_fromValue, 0x4);
GEODE_MEMBER_CHECK(OpacityEffectAction, m_toValue, 0x8);
GEODE_MEMBER_CHECK(OpacityEffectAction, m_finished, 0xc);
GEODE_MEMBER_CHECK(OpacityEffectAction, m_disabled, 0xd);
GEODE_MEMBER_CHECK(OpacityEffectAction, m_deltaTime, 0x10);
GEODE_MEMBER_CHECK(OpacityEffectAction, m_targetGroupID, 0x14);
GEODE_MEMBER_CHECK(OpacityEffectAction, m_currentValue, 0x18);
GEODE_MEMBER_CHECK(OpacityEffectAction, m_triggerUniqueID, 0x1c);
GEODE_MEMBER_CHECK(OpacityEffectAction, m_controlID, 0x20);
GEODE_MEMBER_CHECK(OpacityEffectAction, m_deltaTimeRelated, 0x24);
GEODE_MEMBER_CHECK(OpacityEffectAction, m_durationRelated, 0x28);

GEODE_SIZE_CHECK(TouchToggleAction, 0x2c);
GEODE_MEMBER_CHECK(TouchToggleAction, m_disabled, 0x0);
GEODE_MEMBER_CHECK(TouchToggleAction, m_targetGroupID, 0x4);
GEODE_MEMBER_CHECK(TouchToggleAction, m_holdMode, 0x8);
GEODE_MEMBER_CHECK(TouchToggleAction, m_touchTriggerType, 0xc);
GEODE_MEMBER_CHECK(TouchToggleAction, m_touchTriggerControl, 0x10);
GEODE_MEMBER_CHECK(TouchToggleAction, m_triggerUniqueID, 0x14);
GEODE_MEMBER_CHECK(TouchToggleAction, m_controlID, 0x18);
GEODE_MEMBER_CHECK(TouchToggleAction, m_dualMode, 0x1c);
GEODE_MEMBER_CHECK(TouchToggleAction, m_remapKeys, 0x20);

GEODE_SIZE_CHECK(CollisionTriggerAction, 0x2c);
GEODE_MEMBER_CHECK(CollisionTriggerAction, m_disabled, 0x0);
GEODE_MEMBER_CHECK(CollisionTriggerAction, m_blockAID, 0x4);
GEODE_MEMBER_CHECK(CollisionTriggerAction, m_blockBID, 0x8);
GEODE_MEMBER_CHECK(CollisionTriggerAction, m_targetGroupID, 0xc);
GEODE_MEMBER_CHECK(CollisionTriggerAction, m_triggerOnExit, 0x10);
GEODE_MEMBER_CHECK(CollisionTriggerAction, m_activateGroup, 0x14);
GEODE_MEMBER_CHECK(CollisionTriggerAction, m_triggerUniqueID, 0x18);
GEODE_MEMBER_CHECK(CollisionTriggerAction, m_controlID, 0x1c);
GEODE_MEMBER_CHECK(CollisionTriggerAction, m_remapKeys, 0x20);

GEODE_SIZE_CHECK(ToggleTriggerAction, 0x20);
GEODE_MEMBER_CHECK(ToggleTriggerAction, m_disabled, 0x0);
GEODE_MEMBER_CHECK(ToggleTriggerAction, m_targetGroupID, 0x4);
GEODE_MEMBER_CHECK(ToggleTriggerAction, m_activateGroup, 0x8);
GEODE_MEMBER_CHECK(ToggleTriggerAction, m_triggerUniqueID, 0xc);
GEODE_MEMBER_CHECK(ToggleTriggerAction, m_controlID, 0x10);
GEODE_MEMBER_CHECK(ToggleTriggerAction, m_remapKeys, 0x14);

GEODE_SIZE_CHECK(SpawnTriggerAction, 0x38);
GEODE_MEMBER_CHECK(SpawnTriggerAction, m_finished, 0x0);
GEODE_MEMBER_CHECK(SpawnTriggerAction, m_disabled, 0x1);
GEODE_MEMBER_CHECK(SpawnTriggerAction, m_duration, 0x8);
GEODE_MEMBER_CHECK(SpawnTriggerAction, m_deltaTime, 0x10);
GEODE_MEMBER_CHECK(SpawnTriggerAction, m_targetGroupID, 0x18);
GEODE_MEMBER_CHECK(SpawnTriggerAction, m_triggerUniqueID, 0x1c);
GEODE_MEMBER_CHECK(SpawnTriggerAction, m_controlID, 0x20);
GEODE_MEMBER_CHECK(SpawnTriggerAction, m_spawnOrdered, 0x24);
GEODE_MEMBER_CHECK(SpawnTriggerAction, m_gameObject, 0x28);
GEODE_MEMBER_CHECK(SpawnTriggerAction, m_remapKeys, 0x2c);

GEODE_SIZE_CHECK(tk_spline, 0x70);
GEODE_MEMBER_CHECK(tk_spline, m_x, 0x0);
GEODE_MEMBER_CHECK(tk_spline, m_y, 0xc);
GEODE_MEMBER_CHECK(tk_spline, m_b, 0x18);
GEODE_MEMBER_CHECK(tk_spline, m_c, 0x24);
GEODE_MEMBER_CHECK(tk_spline, m_d, 0x30);
GEODE_MEMBER_CHECK(tk_spline, m_c0, 0x40);
GEODE_MEMBER_CHECK(tk_spline, m_type, 0x48);
GEODE_MEMBER_CHECK(tk_spline, m_left, 0x4c);
GEODE_MEMBER_CHECK(tk_spline, m_right, 0x50);
GEODE_MEMBER_CHECK(tk_spline, m_leftValue, 0x58);
GEODE_MEMBER_CHECK(tk_spline, m_rightValue, 0x60);
GEODE_MEMBER_CHECK(tk_spline, m_madeMonotonic, 0x68);

GEODE_SIZE_CHECK(KeyframeObject, 0x150);
GEODE_MEMBER_CHECK(KeyframeObject, m_unk000, 0x0);
GEODE_MEMBER_CHECK(KeyframeObject, m_unk008, 0x8);
GEODE_MEMBER_CHECK(KeyframeObject, m_unk00c, 0xc);
GEODE_MEMBER_CHECK(KeyframeObject, m_unk010, 0x10);
GEODE_MEMBER_CHECK(KeyframeObject, m_unk014, 0x14);
GEODE_MEMBER_CHECK(KeyframeObject, m_unk018, 0x18);
GEODE_MEMBER_CHECK(KeyframeObject, m_unk01c, 0x1c);
GEODE_MEMBER_CHECK(KeyframeObject, m_unk01d, 0x1d);
GEODE_MEMBER_CHECK(KeyframeObject, m_unk01e, 0x1e);
GEODE_MEMBER_CHECK(KeyframeObject, m_spline1, 0x20);
GEODE_MEMBER_CHECK(KeyframeObject, m_spline2, 0x90);
GEODE_MEMBER_CHECK(KeyframeObject, m_unk170, 0x100);
GEODE_MEMBER_CHECK(KeyframeObject, m_unk178, 0x108);
GEODE_MEMBER_CHECK(KeyframeObject, m_unk180, 0x110);
GEODE_MEMBER_CHECK(KeyframeObject, m_unk184, 0x114);
GEODE_MEMBER_CHECK(KeyframeObject, m_unk188, 0x118);
GEODE_MEMBER_CHECK(KeyframeObject, m_unk190, 0x120);
GEODE_MEMBER_CHECK(KeyframeObject, m_unk198, 0x128);
GEODE_MEMBER_CHECK(KeyframeObject, m_unk1a0, 0x130);
GEODE_MEMBER_CHECK(KeyframeObject, m_unk1a8, 0x138);
GEODE_MEMBER_CHECK(KeyframeObject, m_unk1b0, 0x140);
GEODE_MEMBER_CHECK(KeyframeObject, m_unk1b8, 0x148);

GEODE_SIZE_CHECK(GroupCommandObject2, 0x1e8);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_groupCommandUniqueID, 0x0);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_moveOffset, 0x4);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_easingType, 0xc);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_easingRate, 0x10);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_duration, 0x18);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_deltaTime, 0x20);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_targetGroupID, 0x28);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_centerGroupID, 0x2c);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_currentXOffset, 0x30);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_currentYOffset, 0x38);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_deltaX, 0x40);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_deltaY, 0x48);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_oldDeltaX, 0x50);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_oldDeltaY, 0x58);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_lockedCurrentXOffset, 0x60);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_lockedCurrentYOffset, 0x68);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_finished, 0x70);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_disabled, 0x71);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_finishRelated, 0x72);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_lockToPlayerX, 0x73);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_lockToPlayerY, 0x74);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_lockToCameraX, 0x75);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_lockToCameraY, 0x76);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_lockedInX, 0x77);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_lockedInY, 0x78);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_moveModX, 0x80);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_moveModY, 0x88);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_currentRotateOrTransformValue, 0x90);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_currentRotateOrTransformDelta, 0x98);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_someInterpValue1RelatedOne, 0xa0);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_someInterpValue2RelatedOne, 0xa8);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_rotationOffset, 0xb0);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_lockObjectRotation, 0xb8);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_targetPlayer, 0xbc);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_followXMod, 0xc0);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_followYMod, 0xc8);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_commandType, 0xd0);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_someInterpValue1, 0xd8);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_someInterpValue2, 0xe0);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_keyframeRelated, 0xe8);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_targetScaleX, 0xf0);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_targetScaleY, 0xf8);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_transformTriggerProperty450, 0x100);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_transformTriggerProperty451, 0x108);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_someInterpValue1RelatedZero, 0x110);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_someInterpValue2RelatedZero, 0x118);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_onlyMove, 0x120);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_transformRelatedFalse, 0x121);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_relativeRotation, 0x122);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_someInterpValue1Related, 0x128);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_someInterpValue2Related, 0x130);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_followYSpeed, 0x138);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_followYDelay, 0x140);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_followYOffset, 0x148);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_followYMaxSpeed, 0x150);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_triggerUniqueID, 0x158);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_controlID, 0x15c);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_deltaX_3, 0x160);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_deltaY_3, 0x168);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_oldDeltaX_3, 0x170);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_oldDeltaY_3, 0x178);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_Delta_3_Related, 0x180);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_unkDoubleMaybeUnused, 0x188);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_actionType1, 0x190);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_actionType2, 0x194);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_actionValue1, 0x198);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_actionValue2, 0x1a0);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_someInterpValue1RelatedFalse, 0x1a8);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_deltaTimeInFloat, 0x1ac);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_alreadyUpdated, 0x1b0);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_doUpdate, 0x1b1);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_keyframes, 0x1b4);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_splineRelated, 0x1c0);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_gameObject, 0x1c8);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_gameObjectRotation, 0x1cc);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_remapKeys, 0x1d0);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_someInterpValue2RelatedTrue, 0x1dc);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_unkFloat204, 0x1e0);

GEODE_SIZE_CHECK(TimerItem, 0x48);
GEODE_MEMBER_CHECK(TimerItem, m_itemID, 0x0);
GEODE_MEMBER_CHECK(TimerItem, m_time, 0x8);
GEODE_MEMBER_CHECK(TimerItem, m_paused, 0x10);
GEODE_MEMBER_CHECK(TimerItem, m_timeMod, 0x14);
GEODE_MEMBER_CHECK(TimerItem, m_ignoreTimeWarp, 0x18);
GEODE_MEMBER_CHECK(TimerItem, m_targetTime, 0x20);
GEODE_MEMBER_CHECK(TimerItem, m_stopTimeEnabled, 0x28);
GEODE_MEMBER_CHECK(TimerItem, m_targetGroupID, 0x2c);
GEODE_MEMBER_CHECK(TimerItem, m_triggerUniqueID, 0x30);
GEODE_MEMBER_CHECK(TimerItem, m_controlID, 0x34);
GEODE_MEMBER_CHECK(TimerItem, m_remapKeys, 0x38);
GEODE_MEMBER_CHECK(TimerItem, m_disabled, 0x44);

GEODE_SIZE_CHECK(TimerTriggerAction, 0x2c);
GEODE_MEMBER_CHECK(TimerTriggerAction, m_disabled, 0x0);
GEODE_MEMBER_CHECK(TimerTriggerAction, m_time, 0x4);
GEODE_MEMBER_CHECK(TimerTriggerAction, m_targetTime, 0x8);
GEODE_MEMBER_CHECK(TimerTriggerAction, m_targetGroupID, 0xc);
GEODE_MEMBER_CHECK(TimerTriggerAction, m_triggerUniqueID, 0x10);
GEODE_MEMBER_CHECK(TimerTriggerAction, m_controlID, 0x14);
GEODE_MEMBER_CHECK(TimerTriggerAction, m_itemID, 0x18);
GEODE_MEMBER_CHECK(TimerTriggerAction, m_multiActivate, 0x1c);
GEODE_MEMBER_CHECK(TimerTriggerAction, m_remapKeys, 0x20);

GEODE_SIZE_CHECK(EffectManagerState, 0x184);
GEODE_MEMBER_CHECK(EffectManagerState, m_unkVecCAState, 0x0);
GEODE_MEMBER_CHECK(EffectManagerState, m_unkVecPulseEffectAction, 0xc);
GEODE_MEMBER_CHECK(EffectManagerState, m_unorderedMapInt_vectorPulseEffectAction, 0x18);
GEODE_MEMBER_CHECK(EffectManagerState, m_unorderedMapInt_vectorCountTriggerAction, 0x34);
GEODE_MEMBER_CHECK(EffectManagerState, m_unorderedSet_int1, 0x50);
GEODE_MEMBER_CHECK(EffectManagerState, m_mapInt_Int, 0x6c);
GEODE_MEMBER_CHECK(EffectManagerState, m_unorderedMapInt_OpacityEffectAction, 0x84);
GEODE_MEMBER_CHECK(EffectManagerState, m_vectorTouchToggleAction, 0xa0);
GEODE_MEMBER_CHECK(EffectManagerState, m_vectorCollisionTriggerAction, 0xac);
GEODE_MEMBER_CHECK(EffectManagerState, m_vectorToggleTriggerAction, 0xb8);
GEODE_MEMBER_CHECK(EffectManagerState, m_vectorSpawnTriggerAction, 0xc4);
GEODE_MEMBER_CHECK(EffectManagerState, m_itemCountMap, 0xd0);
GEODE_MEMBER_CHECK(EffectManagerState, m_unorderedMapInt_bool, 0xec);
GEODE_MEMBER_CHECK(EffectManagerState, m_vectorGroupCommandObject2, 0x108);
GEODE_MEMBER_CHECK(EffectManagerState, m_unorderedMapInt_pair_double_double, 0x114);
GEODE_MEMBER_CHECK(EffectManagerState, m_unorderedSet_int2, 0x130);
GEODE_MEMBER_CHECK(EffectManagerState, m_timerItemMap, 0x14c);
GEODE_MEMBER_CHECK(EffectManagerState, m_unorderedMapInt_vectorTimerTriggerAction, 0x168);

GEODE_SIZE_CHECK(SequenceTriggerState, 0x38);
GEODE_MEMBER_CHECK(SequenceTriggerState, m_unkUnorderedMap1, 0x0);
GEODE_MEMBER_CHECK(SequenceTriggerState, m_unkUnorderedMap2, 0x1c);

GEODE_SIZE_CHECK(CheckpointObject, 0xc00);
GEODE_MEMBER_CHECK(CheckpointObject, m_gameState, 0x108);
GEODE_MEMBER_CHECK(CheckpointObject, m_shaderState, 0x5c0);
GEODE_MEMBER_CHECK(CheckpointObject, m_audioState, 0x840);
GEODE_MEMBER_CHECK(CheckpointObject, m_physicalCheckpointObject, 0xa08);
GEODE_MEMBER_CHECK(CheckpointObject, m_player1Checkpoint, 0xa0c);
GEODE_MEMBER_CHECK(CheckpointObject, m_player2Checkpoint, 0xa10);
GEODE_MEMBER_CHECK(CheckpointObject, m_unke78, 0xa14);
GEODE_MEMBER_CHECK(CheckpointObject, m_unke7c, 0xa18);
GEODE_MEMBER_CHECK(CheckpointObject, m_unke80, 0xa1c);
GEODE_MEMBER_CHECK(CheckpointObject, m_ground2Invisible, 0xa20);
GEODE_MEMBER_CHECK(CheckpointObject, m_streakBlend, 0xa21);
GEODE_MEMBER_CHECK(CheckpointObject, m_uniqueID, 0xa24);
GEODE_MEMBER_CHECK(CheckpointObject, m_respawnID, 0xa28);
GEODE_MEMBER_CHECK(CheckpointObject, m_vectorSavedObjectStateRef, 0xa2c);
GEODE_MEMBER_CHECK(CheckpointObject, m_vectorActiveSaveObjectState, 0xa38);
GEODE_MEMBER_CHECK(CheckpointObject, m_vectorSpecialSaveObjectState, 0xa44);
GEODE_MEMBER_CHECK(CheckpointObject, m_effectManagerState, 0xa50);
GEODE_MEMBER_CHECK(CheckpointObject, m_gradientTriggerObjectArray, 0xbd4);
GEODE_MEMBER_CHECK(CheckpointObject, m_unk11e8, 0xbd8);
GEODE_MEMBER_CHECK(CheckpointObject, m_sequenceTriggerStateUnorderedMap, 0xbdc);
GEODE_MEMBER_CHECK(CheckpointObject, m_commandIndex, 0xbf8);

#endif
