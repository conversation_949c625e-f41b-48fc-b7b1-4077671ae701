#include "Common.hpp"

#ifdef GEODE_IS_ANDROID64

using namespace geode::prelude;

GEODE_SIZE_CHECK(CCObject, 0x38);
GEODE_SIZE_CHECK(CCNode, 0x140);
GEODE_SIZE_CHECK(CCNodeRGBA, 0x158);
GEODE_SIZE_CHECK(CCLayer, 0x198);
GEODE_SIZE_CHECK(CCLayerColor, 0x220);
GEODE_SIZE_CHECK(CCSprite, 0x258);
GEODE_SIZE_CHECK(CCLayer, 0x198);

GEODE_SIZE_CHECK(CCMenu, 0x1b8);
GEODE_SIZE_CHECK(CCMenuItem, 0x178);
GEODE_SIZE_CHECK(CCMenuItemSprite, 0x190);

GEODE_MEMBER_CHECK(TableView, m_cellDelegate, 0x268);
GEODE_MEMBER_CHECK(CCTextInputNode, m_textField, 0x1f8);

GEODE_SIZE_CHECK(GJBaseGameLayer, 0x3690);
GEODE_MEMBER_CHECK(GJBaseGameLayer, m_player1, 0xdb8);
GEODE_MEMBER_CHECK(GJBaseGameLayer, m_player2, 0xdc0);
GEODE_MEMBER_CHECK(GJBaseGameLayer, m_groupDict, 0xf10);
GEODE_MEMBER_CHECK(GJBaseGameLayer, m_parentGroupsDict, 0xf70);
GEODE_MEMBER_CHECK(GJBaseGameLayer, m_linkedGroupDict, 0xfc8);
GEODE_MEMBER_CHECK(GJBaseGameLayer, m_objectLayer, 0xff0);
GEODE_MEMBER_CHECK(GJBaseGameLayer, m_groundLayer, 0x1018);
GEODE_MEMBER_CHECK(GJBaseGameLayer, m_groundLayer2, 0x1020);
GEODE_MEMBER_CHECK(GJBaseGameLayer, m_varianceValues, 0x10e4);
GEODE_MEMBER_CHECK(GJBaseGameLayer, m_particlesDict, 0x30f0);
GEODE_MEMBER_CHECK(GJBaseGameLayer, m_customParticles, 0x30f8);
GEODE_MEMBER_CHECK(GJBaseGameLayer, m_unclaimedParticles, 0x3100);
GEODE_MEMBER_CHECK(GJBaseGameLayer, m_claimedParticles, 0x3140);
GEODE_MEMBER_CHECK(GJBaseGameLayer, m_temporaryParticles, 0x3148);
GEODE_MEMBER_CHECK(GJBaseGameLayer, m_attempts, 0x30b4);
GEODE_MEMBER_CHECK(GJBaseGameLayer, m_shaderLayer, 0x3198);
GEODE_MEMBER_CHECK(GJBaseGameLayer, m_startPosObject, 0x31a8);
GEODE_MEMBER_CHECK(GJBaseGameLayer, m_isPracticeMode, 0x3210);
GEODE_MEMBER_CHECK(GJBaseGameLayer, m_loadingProgress, 0x3214);
GEODE_MEMBER_CHECK(GJBaseGameLayer, m_collectedItems, 0x3238);
GEODE_MEMBER_CHECK(GJBaseGameLayer, m_endPortal, 0x3248);
GEODE_MEMBER_CHECK(GJBaseGameLayer, m_queuedButtons, 0x3308);
GEODE_MEMBER_CHECK(GJBaseGameLayer, m_uiLayer, 0x3468);
// GEODE_MEMBER_CHECK(GJBaseGameLayer, m_started, 0x3490); // wtf is this
GEODE_MEMBER_CHECK(GJBaseGameLayer, m_sections, 0x34a0);
GEODE_MEMBER_CHECK(GJBaseGameLayer, m_sectionSizes, 0x3548);
GEODE_MEMBER_CHECK(GJBaseGameLayer, m_audioVisualizerBG, 0x35f0);
GEODE_MEMBER_CHECK(GJBaseGameLayer, m_loadingLayer, 0x3668);


GEODE_SIZE_CHECK(PlayLayer, 0x3978);
GEODE_MEMBER_CHECK(PlayLayer, m_circleWaveArray, 0x37b0);
GEODE_MEMBER_CHECK(PlayLayer, m_attemptLabel, 0x37d0);
GEODE_MEMBER_CHECK(PlayLayer, m_progressBar, 0x37e8);
GEODE_MEMBER_CHECK(PlayLayer, m_jumps, 0x3860);
GEODE_MEMBER_CHECK(PlayLayer, m_hasCompletedLevel, 0x386d);
GEODE_MEMBER_CHECK(PlayLayer, m_isPaused, 0x38df);
GEODE_MEMBER_CHECK(PlayLayer, m_infoLabel, 0x38e8);

GEODE_SIZE_CHECK(DrawGridLayer, 0x260);
GEODE_MEMBER_CHECK(DrawGridLayer, m_editorLayer, 0x200);
GEODE_MEMBER_CHECK(DrawGridLayer, m_gridSize, 0x25c);

GEODE_SIZE_CHECK(LevelEditorLayer, 0x39d8);
GEODE_MEMBER_CHECK(LevelEditorLayer, m_trailTimer, 0x3788);
GEODE_MEMBER_CHECK(LevelEditorLayer, m_drawGridLayer, 0x37b8);

GEODE_SIZE_CHECK(GameObject, 0x538);
GEODE_MEMBER_CHECK(GameObject, m_particleString, 0x310);

GEODE_SIZE_CHECK(PlayerObject, 0xc38);
GEODE_MEMBER_CHECK(PlayerObject, m_spiderSprite, 0x878);
GEODE_MEMBER_CHECK(PlayerObject, m_maybeLastGroundObject, 0x590);
GEODE_MEMBER_CHECK(PlayerObject, m_dashFireSprite, 0x648);
GEODE_MEMBER_CHECK(PlayerObject, m_particleSystems, 0x678);
GEODE_MEMBER_CHECK(PlayerObject, m_isHidden, 0x703);
GEODE_MEMBER_CHECK(PlayerObject, m_ghostTrail, 0x708);
GEODE_MEMBER_CHECK(PlayerObject, m_iconSprite, 0x710);
GEODE_MEMBER_CHECK(PlayerObject, m_waveTrail, 0x788);
GEODE_MEMBER_CHECK(PlayerObject, m_switchWaveTrailColor, 0x802);
GEODE_MEMBER_CHECK(PlayerObject, m_robotSprite, 0x870);
GEODE_MEMBER_CHECK(PlayerObject, m_landParticles1, 0x8e0);
GEODE_MEMBER_CHECK(PlayerObject, m_hasCustomGlowColor, 0x950);
GEODE_MEMBER_CHECK(PlayerObject, m_isDart, 0x98c);
GEODE_MEMBER_CHECK(PlayerObject, m_vehicleSize, 0x9c0);
GEODE_MEMBER_CHECK(PlayerObject, m_touchingRings, 0xa08);
GEODE_MEMBER_CHECK(PlayerObject, m_hasEverJumped, 0xa50);
GEODE_MEMBER_CHECK(PlayerObject, m_position, 0xa58);
GEODE_MEMBER_CHECK(PlayerObject, m_playerFollowFloats, 0xa88);
GEODE_MEMBER_CHECK(PlayerObject, m_platformerXVelocity, 0xac0);
GEODE_MEMBER_CHECK(PlayerObject, m_isPlatformer, 0xb38);
GEODE_MEMBER_CHECK(PlayerObject, m_robotBatchNode, 0xbe8);
GEODE_MEMBER_CHECK(PlayerObject, m_gv0123, 0xbe0);
GEODE_MEMBER_CHECK(PlayerObject, m_gameLayer, 0xc10);

// following are from 2.205 so might not all be right

GEODE_MEMBER_CHECK(SimplePlayer, m_hasCustomGlowColor, 0x29c);

GEODE_MEMBER_CHECK(GameManager, m_playLayer, 0x1d8);
GEODE_MEMBER_CHECK(GameManager, m_levelEditorLayer, 0x1e0);
GEODE_MEMBER_CHECK(GameManager, m_gameLayer, 0x1e8);

GEODE_SIZE_CHECK(FMODAudioEngine, 0x820);
GEODE_MEMBER_CHECK(FMODAudioEngine, m_musicVolume, 0x1e8);
GEODE_MEMBER_CHECK(FMODAudioEngine, m_sfxVolume, 0x1ec);
GEODE_MEMBER_CHECK(FMODAudioEngine, m_pulse1, 0x1f8);
GEODE_MEMBER_CHECK(FMODAudioEngine, m_pulse2, 0x1fc);
GEODE_MEMBER_CHECK(FMODAudioEngine, m_pulse3, 0x200);
GEODE_MEMBER_CHECK(FMODAudioEngine, m_metering, 0x208);
GEODE_MEMBER_CHECK(FMODAudioEngine, m_system, 0x218);

GEODE_SIZE_CHECK(SimplePlayer, 0x2a8);
GEODE_SIZE_CHECK(EnhancedGameObject, 0x598);
GEODE_SIZE_CHECK(EffectGameObject, 0x720);

GEODE_MEMBER_CHECK(GameObject, m_hasExtendedCollision, 0x27c);
GEODE_MEMBER_CHECK(GameObject, m_activeMainColorID, 0x290);
GEODE_MEMBER_CHECK(GameObject, m_glowSprite, 0x2f0);
GEODE_MEMBER_CHECK(GameObject, m_colorSprite, 0x368);
GEODE_MEMBER_CHECK(GameObject, m_baseColor, 0x438);
GEODE_MEMBER_CHECK(GameObject, m_groups, 0x478);
GEODE_MEMBER_CHECK(GameObject, m_isHighDetail, 0x4db);
GEODE_MEMBER_CHECK(GameObject, m_isPassable, 0x4fd);
GEODE_MEMBER_CHECK(GameObject, m_mainColorKeyIndex, 0x518);

GEODE_MEMBER_CHECK(LevelInfoLayer, m_circle, 0x278);
GEODE_MEMBER_CHECK(LevelInfoLayer, m_challenge, 0x2b0);
GEODE_MEMBER_CHECK(LevelInfoLayer, m_songWidget, 0x2d0);

GEODE_MEMBER_CHECK(LevelEditorLayer, m_editorUI, 0x3790);

GEODE_SIZE_CHECK(FLAlertLayer, 0x278);
GEODE_SIZE_CHECK(BoomListView, 0x1d0);
GEODE_SIZE_CHECK(CustomListView, 0x1d8);
GEODE_SIZE_CHECK(CCMenuItemSpriteExtra, 0x1d8);
GEODE_SIZE_CHECK(LoadingLayer, 0x1c8);
GEODE_SIZE_CHECK(GJDropDownLayer, 0x260);
GEODE_SIZE_CHECK(Slider, 0x1c8);
GEODE_SIZE_CHECK(SliderTouchLogic, 0x1e0);
GEODE_SIZE_CHECK(CCScrollLayerExt, 0x200);
GEODE_SIZE_CHECK(TableView, 0x290);
GEODE_SIZE_CHECK(CCTextInputNode, 0x238);
GEODE_SIZE_CHECK(CCTouchDispatcher, 0x90);
GEODE_SIZE_CHECK(AccountLayer, 0x2d0);
GEODE_SIZE_CHECK(SetupTriggerPopup, 0x3c8);
GEODE_SIZE_CHECK(GJOptionsLayer, 0x408);

GEODE_SIZE_CHECK(TextArea, 0x2b8);
GEODE_MEMBER_CHECK(TextArea, m_disableColor, 0x254);
GEODE_MEMBER_CHECK(TextArea, m_label, 0x258);
GEODE_MEMBER_CHECK(TextArea, m_width, 0x260);
GEODE_MEMBER_CHECK(TextArea, m_unknown, 0x264);
GEODE_MEMBER_CHECK(TextArea, m_fontFile, 0x268);
GEODE_MEMBER_CHECK(TextArea, m_height, 0x270);
GEODE_MEMBER_CHECK(TextArea, m_unkBool, 0x274);
GEODE_MEMBER_CHECK(TextArea, m_anchorPoint, 0x278);
GEODE_MEMBER_CHECK(TextArea, m_allShown, 0x280);
GEODE_MEMBER_CHECK(TextArea, m_scale, 0x284);
GEODE_MEMBER_CHECK(TextArea, m_rectHeight, 0x288);
GEODE_MEMBER_CHECK(TextArea, m_rectWidth, 0x28c);
GEODE_MEMBER_CHECK(TextArea, m_maxWidth, 0x290);
GEODE_MEMBER_CHECK(TextArea, m_unkPoint, 0x294);
GEODE_MEMBER_CHECK(TextArea, m_delegate, 0x2a0);
GEODE_MEMBER_CHECK(TextArea, m_shakeCharacters, 0x2a8);
GEODE_MEMBER_CHECK(TextArea, m_shakeElapsed, 0x2b0);

GEODE_SIZE_CHECK(TableViewCell, 0x218);
GEODE_MEMBER_CHECK(TableViewCell, m_tableView, 0x1a0);
GEODE_MEMBER_CHECK(TableViewCell, m_indexPath, 0x1a8);
GEODE_MEMBER_CHECK(TableViewCell, m_unknownString, 0x1f0);
GEODE_MEMBER_CHECK(TableViewCell, m_width, 0x1f8);
GEODE_MEMBER_CHECK(TableViewCell, m_height, 0x1fc);
GEODE_MEMBER_CHECK(TableViewCell, m_mainLayer, 0x208);

GEODE_MEMBER_CHECK(TableView, m_cellDelegate, 0x268);
GEODE_MEMBER_CHECK(CCTextInputNode, m_textField, 0x1f8);

GEODE_MEMBER_CHECK(GameLevelManager, m_commentUploadDelegate, 0x2e8);

GEODE_MEMBER_CHECK(GJAccountManager, m_username, 0x148);
GEODE_MEMBER_CHECK(GJAccountManager, m_GJP2, 0x160);

GEODE_SIZE_CHECK(EditorUI, 0x598);
GEODE_MEMBER_CHECK(EditorUI, m_customTabBar, 0x258);
GEODE_MEMBER_CHECK(EditorUI, m_editButtonBar, 0x2f8);
GEODE_MEMBER_CHECK(EditorUI, m_positionSlider, 0x300);
GEODE_MEMBER_CHECK(EditorUI, m_selectedObjects, 0x338);
GEODE_MEMBER_CHECK(EditorUI, m_selectedMode, 0x4e8);
GEODE_MEMBER_CHECK(EditorUI, m_selectedObject, 0x528);
GEODE_MEMBER_CHECK(EditorUI, m_selectedTab, 0x548);

GEODE_MEMBER_CHECK(ChallengesPage, m_dots, 0x290);
GEODE_MEMBER_CHECK(ChallengesPage, m_challengeNodes, 0x2B8);

GEODE_SIZE_CHECK(GJEffectManager, 0x770);
GEODE_MEMBER_CHECK(GJEffectManager, m_unkDict150, 0x150);
GEODE_MEMBER_CHECK(GJEffectManager, m_pulseEffectVector, 0x158);
GEODE_MEMBER_CHECK(GJEffectManager, m_pulseEffectMap, 0x170);
GEODE_MEMBER_CHECK(GJEffectManager, m_pulseEffectMap, 0x170);
GEODE_MEMBER_CHECK(GJEffectManager, m_colorActionDict, 0x260);
GEODE_MEMBER_CHECK(GJEffectManager, m_itemCountMap, 0x318);
GEODE_MEMBER_CHECK(GJEffectManager, m_persistentItemCountMap, 0x350);
GEODE_MEMBER_CHECK(GJEffectManager, m_persistentTimerItemSet, 0x388);
GEODE_MEMBER_CHECK(GJEffectManager, m_timerItemMap, 0x3c0);
GEODE_MEMBER_CHECK(GJEffectManager, m_unkVector708, 0x708);

GEODE_MEMBER_CHECK(HardStreak, m_pointArray, 0x1a0);

GEODE_MEMBER_CHECK(ShaderLayer, m_shader, 0x458);
GEODE_MEMBER_CHECK(ShaderLayer, m_gameLayer, 0x480);
GEODE_MEMBER_CHECK(ShaderLayer, m_splitYRangeMultUniform, 0x654);

GEODE_MEMBER_CHECK(SetupInstantCountPopup, m_itemID, 0x3e0);

GEODE_SIZE_CHECK(CCLightFlash, 0x168);
GEODE_SIZE_CHECK(CCLightStrip, 0x160);

GEODE_SIZE_CHECK(GJValueTween, 0x28);
GEODE_SIZE_CHECK(GameObjectPhysics, 0x30);
GEODE_SIZE_CHECK(EventTriggerInstance, 0x28);

GEODE_SIZE_CHECK(EnterEffectInstance, 0x108);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_enterEffectAnimMap, 0x0);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_length, 0x30);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_lengthVariance, 0x34);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_offset, 0x38);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_offsetVariance, 0x3c);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_offsetY, 0x40);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_offsetYVariance, 0x44);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_modFront, 0x48);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_modBack, 0x4c);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_deadzone, 0x50);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_moveDistance, 0x54);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_moveDistanceVariance, 0x58);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_moveAngle, 0x5c);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_moveAngleVariance, 0x60);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_moveX, 0x64);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_moveXVariance, 0x68);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_moveY, 0x6c);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_moveYVariance, 0x70);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_relativeFade, 0x74);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_scaleX, 0x78);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_scaleXVariance, 0x7c);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_scaleY, 0x80);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_scaleYVariance, 0x84);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_rotation, 0x88);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_rotationVariance, 0x8c);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_tint, 0x90);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_unk074, 0x94);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_toOpacity, 0x98);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_fromOpacity, 0x9c);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_hsvValue, 0xa0);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_hue, 0xb0);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_saturation, 0xb4);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_value, 0xb8);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_gameObject, 0xc0);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_unkBool1, 0xc8);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_targetID, 0xcc);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_centerID, 0xd0);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_unkFloat3, 0xd4);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_unkFloat4, 0xd8);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_unkBool2, 0xdc);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_unkBool3, 0xdd);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_unkBool4, 0xde);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_unkFloat5, 0xe0);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_unkVecInt, 0xe8);
GEODE_MEMBER_CHECK(EnterEffectInstance, m_controlID, 0x100);

GEODE_SIZE_CHECK(AdvancedFollowInstance, 0x20);
GEODE_MEMBER_CHECK(AdvancedFollowInstance, m_gameObject, 0x0);
GEODE_MEMBER_CHECK(AdvancedFollowInstance, m_group, 0x8);
GEODE_MEMBER_CHECK(AdvancedFollowInstance, m_objectKey, 0xc);
GEODE_MEMBER_CHECK(AdvancedFollowInstance, m_controlId, 0x10);
GEODE_MEMBER_CHECK(AdvancedFollowInstance, m_otherObjectKey, 0x14);
GEODE_MEMBER_CHECK(AdvancedFollowInstance, m_relatedToGJGameStateUnkUint7, 0x18);
GEODE_MEMBER_CHECK(AdvancedFollowInstance, m_finished, 0x1c);
GEODE_MEMBER_CHECK(AdvancedFollowInstance, m_doStart, 0x1d);
GEODE_MEMBER_CHECK(AdvancedFollowInstance, m_started, 0x1e);
GEODE_MEMBER_CHECK(AdvancedFollowInstance, m_processed, 0x1f);

GEODE_SIZE_CHECK(DynamicObjectAction, 0x60);
GEODE_MEMBER_CHECK(DynamicObjectAction, m_gameObject1, 0x0);
GEODE_MEMBER_CHECK(DynamicObjectAction, m_gameObject2, 0x8);
GEODE_MEMBER_CHECK(DynamicObjectAction, m_gameObject3, 0x10);
GEODE_MEMBER_CHECK(DynamicObjectAction, m_gameObject4, 0x18);
GEODE_MEMBER_CHECK(DynamicObjectAction, m_gameObject5, 0x20);
GEODE_MEMBER_CHECK(DynamicObjectAction, m_gameObject6, 0x28);
GEODE_MEMBER_CHECK(DynamicObjectAction, m_gameObject7, 0x30);
GEODE_MEMBER_CHECK(DynamicObjectAction, m_gameObject8, 0x38);
GEODE_MEMBER_CHECK(DynamicObjectAction, m_unkFloat1, 0x40);
GEODE_MEMBER_CHECK(DynamicObjectAction, m_unkFloat2, 0x44);
GEODE_MEMBER_CHECK(DynamicObjectAction, m_unkFloat3, 0x48);
GEODE_MEMBER_CHECK(DynamicObjectAction, m_unkBool1, 0x4c);
GEODE_MEMBER_CHECK(DynamicObjectAction, m_unkBool2, 0x4d);
GEODE_MEMBER_CHECK(DynamicObjectAction, m_unkBool3, 0x4e);
GEODE_MEMBER_CHECK(DynamicObjectAction, m_unkBool4, 0x4f);
GEODE_MEMBER_CHECK(DynamicObjectAction, m_unkFloat4, 0x50);
GEODE_MEMBER_CHECK(DynamicObjectAction, m_unkFloat5, 0x54);
GEODE_MEMBER_CHECK(DynamicObjectAction, m_unkFloat6, 0x58);

GEODE_SIZE_CHECK(SFXTriggerInstance, 0x18);
GEODE_MEMBER_CHECK(SFXTriggerInstance, m_groupID1, 0x0);
GEODE_MEMBER_CHECK(SFXTriggerInstance, m_groupID2, 0x4);
GEODE_MEMBER_CHECK(SFXTriggerInstance, m_controlID, 0x8);
GEODE_MEMBER_CHECK(SFXTriggerInstance, m_sfxTriggerGameObject, 0x10);

GEODE_SIZE_CHECK(SongChannelState, 0x20);
GEODE_MEMBER_CHECK(SongChannelState, m_songTriggerGameObject1, 0x0);
GEODE_MEMBER_CHECK(SongChannelState, m_unkDouble1, 0x8);
GEODE_MEMBER_CHECK(SongChannelState, m_songTriggerGameObject2, 0x10);
GEODE_MEMBER_CHECK(SongChannelState, m_unkDouble2, 0x18);

GEODE_SIZE_CHECK(SongTriggerState, 0x10);
GEODE_MEMBER_CHECK(SongTriggerState, m_songTriggerGameObject, 0x0);
GEODE_MEMBER_CHECK(SongTriggerState, m_unkDouble, 0x8);

GEODE_SIZE_CHECK(SFXStateContainer, 0x20);
GEODE_MEMBER_CHECK(SFXStateContainer, m_unkDouble1, 0x0);
GEODE_MEMBER_CHECK(SFXStateContainer, m_unkDouble2, 0x8);
GEODE_MEMBER_CHECK(SFXStateContainer, m_unkFloat1, 0x10);
GEODE_MEMBER_CHECK(SFXStateContainer, m_unkFloat2, 0x14);
GEODE_MEMBER_CHECK(SFXStateContainer, m_unkBool, 0x18);

GEODE_SIZE_CHECK(SFXTriggerState, 0xa8);
GEODE_MEMBER_CHECK(SFXTriggerState, m_sfxTriggerGameObject, 0x0);
GEODE_MEMBER_CHECK(SFXTriggerState, m_unkInt1, 0x8);
GEODE_MEMBER_CHECK(SFXTriggerState, m_unkDouble1, 0x10);
GEODE_MEMBER_CHECK(SFXTriggerState, m_unkDouble2, 0x18);
GEODE_MEMBER_CHECK(SFXTriggerState, m_unkDouble3, 0x20);
GEODE_MEMBER_CHECK(SFXTriggerState, m_unkDouble4, 0x28);
GEODE_MEMBER_CHECK(SFXTriggerState, m_unkFloat1, 0x30);
GEODE_MEMBER_CHECK(SFXTriggerState, m_unkFloat2, 0x34);
GEODE_MEMBER_CHECK(SFXTriggerState, m_unkFloat3, 0x38);
GEODE_MEMBER_CHECK(SFXTriggerState, m_unkFloat4, 0x3c);
GEODE_MEMBER_CHECK(SFXTriggerState, m_unkInt2, 0x40);
GEODE_MEMBER_CHECK(SFXTriggerState, m_processed, 0x44);
GEODE_MEMBER_CHECK(SFXTriggerState, m_unkBool1, 0x45);
GEODE_MEMBER_CHECK(SFXTriggerState, m_sfxStateContainers, 0x48);

GEODE_SIZE_CHECK(GJGameState, 0x6f0);
GEODE_MEMBER_CHECK(GJGameState, m_cameraZoom, 0x0);
GEODE_MEMBER_CHECK(GJGameState, m_targetCameraZoom, 0x4);
GEODE_MEMBER_CHECK(GJGameState, m_cameraOffset, 0x8);
GEODE_MEMBER_CHECK(GJGameState, m_unkPoint1, 0x10);
GEODE_MEMBER_CHECK(GJGameState, m_unkPoint2, 0x18);
GEODE_MEMBER_CHECK(GJGameState, m_unkPoint3, 0x20);
GEODE_MEMBER_CHECK(GJGameState, m_unkPoint4, 0x28);
GEODE_MEMBER_CHECK(GJGameState, m_unkPoint5, 0x30);
GEODE_MEMBER_CHECK(GJGameState, m_unkPoint6, 0x38);
GEODE_MEMBER_CHECK(GJGameState, m_unkPoint7, 0x40);
GEODE_MEMBER_CHECK(GJGameState, m_unkPoint8, 0x48);
GEODE_MEMBER_CHECK(GJGameState, m_unkPoint9, 0x50);
GEODE_MEMBER_CHECK(GJGameState, m_unkPoint10, 0x58);
GEODE_MEMBER_CHECK(GJGameState, m_unkPoint11, 0x60);
GEODE_MEMBER_CHECK(GJGameState, m_unkPoint12, 0x68);
GEODE_MEMBER_CHECK(GJGameState, m_unkPoint13, 0x70);
GEODE_MEMBER_CHECK(GJGameState, m_unkPoint14, 0x78);
GEODE_MEMBER_CHECK(GJGameState, m_unkPoint15, 0x80);
GEODE_MEMBER_CHECK(GJGameState, m_unkPoint16, 0x88);
GEODE_MEMBER_CHECK(GJGameState, m_unkPoint17, 0x90);
GEODE_MEMBER_CHECK(GJGameState, m_unkPoint18, 0x98);
GEODE_MEMBER_CHECK(GJGameState, m_unkPoint19, 0xa0);
GEODE_MEMBER_CHECK(GJGameState, m_unkPoint20, 0xa8);
GEODE_MEMBER_CHECK(GJGameState, m_unkPoint21, 0xb0);
GEODE_MEMBER_CHECK(GJGameState, m_unkPoint22, 0xb8);
GEODE_MEMBER_CHECK(GJGameState, m_unkPoint23, 0xc0);
GEODE_MEMBER_CHECK(GJGameState, m_unkPoint24, 0xc8);
GEODE_MEMBER_CHECK(GJGameState, m_unkPoint25, 0xd0);
GEODE_MEMBER_CHECK(GJGameState, m_unkPoint26, 0xd8);
GEODE_MEMBER_CHECK(GJGameState, m_unkPoint27, 0xe0);
GEODE_MEMBER_CHECK(GJGameState, m_unkPoint28, 0xe8);
GEODE_MEMBER_CHECK(GJGameState, m_unkPoint29, 0xf0);
GEODE_MEMBER_CHECK(GJGameState, m_unkBool1, 0xf8);
GEODE_MEMBER_CHECK(GJGameState, m_unkInt1, 0xfc);
GEODE_MEMBER_CHECK(GJGameState, m_unkBool2, 0x100);
GEODE_MEMBER_CHECK(GJGameState, m_unkInt2, 0x104);
GEODE_MEMBER_CHECK(GJGameState, m_unkBool3, 0x108);
GEODE_MEMBER_CHECK(GJGameState, m_unkPoint30, 0x10c);
GEODE_MEMBER_CHECK(GJGameState, m_middleGroundOffsetY, 0x114);
GEODE_MEMBER_CHECK(GJGameState, m_unkInt3, 0x118);
GEODE_MEMBER_CHECK(GJGameState, m_unkInt4, 0x11c);
GEODE_MEMBER_CHECK(GJGameState, m_unkBool4, 0x120);
GEODE_MEMBER_CHECK(GJGameState, m_unkBool5, 0x121);
GEODE_MEMBER_CHECK(GJGameState, m_unkFloat2, 0x124);
GEODE_MEMBER_CHECK(GJGameState, m_unkFloat3, 0x128);
GEODE_MEMBER_CHECK(GJGameState, m_unkInt5, 0x12c);
GEODE_MEMBER_CHECK(GJGameState, m_unkInt6, 0x130);
GEODE_MEMBER_CHECK(GJGameState, m_unkInt7, 0x134);
GEODE_MEMBER_CHECK(GJGameState, m_unkInt8, 0x138);
GEODE_MEMBER_CHECK(GJGameState, m_unkInt9, 0x13c);
GEODE_MEMBER_CHECK(GJGameState, m_unkInt10, 0x140);
GEODE_MEMBER_CHECK(GJGameState, m_unkInt11, 0x144);
GEODE_MEMBER_CHECK(GJGameState, m_unkFloat4, 0x148);
GEODE_MEMBER_CHECK(GJGameState, m_unkUint1, 0x14c);
GEODE_MEMBER_CHECK(GJGameState, m_portalY, 0x150);
GEODE_MEMBER_CHECK(GJGameState, m_unkBool6, 0x154);
GEODE_MEMBER_CHECK(GJGameState, m_gravityRelated, 0x155);
GEODE_MEMBER_CHECK(GJGameState, m_unkInt12, 0x158);
GEODE_MEMBER_CHECK(GJGameState, m_unkInt13, 0x15c);
GEODE_MEMBER_CHECK(GJGameState, m_unkInt14, 0x160);
GEODE_MEMBER_CHECK(GJGameState, m_unkInt15, 0x164);
GEODE_MEMBER_CHECK(GJGameState, m_unkBool7, 0x168);
GEODE_MEMBER_CHECK(GJGameState, m_unkBool8, 0x169);
GEODE_MEMBER_CHECK(GJGameState, m_unkBool9, 0x16a);
GEODE_MEMBER_CHECK(GJGameState, m_unkFloat5, 0x16c);
GEODE_MEMBER_CHECK(GJGameState, m_unkFloat6, 0x170);
GEODE_MEMBER_CHECK(GJGameState, m_unkFloat7, 0x174);
GEODE_MEMBER_CHECK(GJGameState, m_unkFloat8, 0x178);
GEODE_MEMBER_CHECK(GJGameState, m_cameraAngle, 0x17c);
GEODE_MEMBER_CHECK(GJGameState, m_targetCameraAngle, 0x180);
GEODE_MEMBER_CHECK(GJGameState, m_playerStreakBlend, 0x184);
GEODE_MEMBER_CHECK(GJGameState, m_timeWarp, 0x188);
GEODE_MEMBER_CHECK(GJGameState, m_timeWarpRelated, 0x18c);
GEODE_MEMBER_CHECK(GJGameState, m_currentChannel, 0x190);
GEODE_MEMBER_CHECK(GJGameState, m_rotateChannel, 0x194);
GEODE_MEMBER_CHECK(GJGameState, m_spawnChannelRelated0, 0x198);
GEODE_MEMBER_CHECK(GJGameState, m_spawnChannelRelated1, 0x1d0);
GEODE_MEMBER_CHECK(GJGameState, m_totalTime, 0x208);
GEODE_MEMBER_CHECK(GJGameState, m_levelTime, 0x210);
GEODE_MEMBER_CHECK(GJGameState, m_unkDouble3, 0x218);
GEODE_MEMBER_CHECK(GJGameState, m_unkUint2, 0x220);
GEODE_MEMBER_CHECK(GJGameState, m_unkUint3, 0x224);
GEODE_MEMBER_CHECK(GJGameState, m_currentProgress, 0x228);
GEODE_MEMBER_CHECK(GJGameState, m_unkUint4, 0x22c);
GEODE_MEMBER_CHECK(GJGameState, m_unkUint5, 0x230);
GEODE_MEMBER_CHECK(GJGameState, m_unkUint6, 0x234);
GEODE_MEMBER_CHECK(GJGameState, m_unkUint7, 0x238);
GEODE_MEMBER_CHECK(GJGameState, m_unkGameObjPtr1, 0x240);
GEODE_MEMBER_CHECK(GJGameState, m_unkGameObjPtr2, 0x248);
GEODE_MEMBER_CHECK(GJGameState, m_cameraPosition, 0x250);
GEODE_MEMBER_CHECK(GJGameState, m_unkBool10, 0x258);
GEODE_MEMBER_CHECK(GJGameState, m_levelFlipping, 0x25c);
GEODE_MEMBER_CHECK(GJGameState, m_unkBool11, 0x260);
GEODE_MEMBER_CHECK(GJGameState, m_unkBool12, 0x261);
GEODE_MEMBER_CHECK(GJGameState, m_isDualMode, 0x262);
GEODE_MEMBER_CHECK(GJGameState, m_unkFloat9, 0x264);
GEODE_MEMBER_CHECK(GJGameState, m_tweenActions, 0x268);
GEODE_MEMBER_CHECK(GJGameState, m_cameraEdgeValue0, 0x2a0);
GEODE_MEMBER_CHECK(GJGameState, m_cameraEdgeValue1, 0x2a4);
GEODE_MEMBER_CHECK(GJGameState, m_cameraEdgeValue2, 0x2a8);
GEODE_MEMBER_CHECK(GJGameState, m_cameraEdgeValue3, 0x2ac);
GEODE_MEMBER_CHECK(GJGameState, m_gameObjectPhysics, 0x2b0);
GEODE_MEMBER_CHECK(GJGameState, m_unkVecFloat1, 0x2e8);
GEODE_MEMBER_CHECK(GJGameState, m_unkUint10, 0x300);
GEODE_MEMBER_CHECK(GJGameState, m_unkUint11, 0x304);
GEODE_MEMBER_CHECK(GJGameState, m_unkUint12, 0x308);
GEODE_MEMBER_CHECK(GJGameState, m_cameraStepDiff, 0x30c);
GEODE_MEMBER_CHECK(GJGameState, m_unkFloat10, 0x314);
GEODE_MEMBER_CHECK(GJGameState, m_timeModRelated, 0x318);
GEODE_MEMBER_CHECK(GJGameState, m_timeModRelated2, 0x31c);
GEODE_MEMBER_CHECK(GJGameState, m_unkMapPairIntIntInt, 0x320);
GEODE_MEMBER_CHECK(GJGameState, m_unkUint13, 0x350);
GEODE_MEMBER_CHECK(GJGameState, m_unkPoint32, 0x354);
GEODE_MEMBER_CHECK(GJGameState, m_unkPoint33, 0x35c);
GEODE_MEMBER_CHECK(GJGameState, m_unkBool20, 0x364);
GEODE_MEMBER_CHECK(GJGameState, m_unkBool21, 0x365);
GEODE_MEMBER_CHECK(GJGameState, m_unkBool22, 0x366);
GEODE_MEMBER_CHECK(GJGameState, m_unkUint14, 0x368);
GEODE_MEMBER_CHECK(GJGameState, m_unkBool26, 0x36c);
GEODE_MEMBER_CHECK(GJGameState, m_cameraShakeEnabled, 0x36d);
GEODE_MEMBER_CHECK(GJGameState, m_cameraShakeFactor, 0x370);
GEODE_MEMBER_CHECK(GJGameState, m_unkUint15, 0x374);
GEODE_MEMBER_CHECK(GJGameState, m_unkUint16, 0x378);
GEODE_MEMBER_CHECK(GJGameState, m_unkUint64_1, 0x380);
GEODE_MEMBER_CHECK(GJGameState, m_unkPoint34, 0x388);
GEODE_MEMBER_CHECK(GJGameState, m_dualRelated, 0x390);
GEODE_MEMBER_CHECK(GJGameState, m_stateObjects, 0x398);
GEODE_MEMBER_CHECK(GJGameState, m_unkMapPairGJGameEventIntVectorEventTriggerInstance, 0x3d0);
GEODE_MEMBER_CHECK(GJGameState, m_unkMapPairGJGameEventIntInt, 0x400);
GEODE_MEMBER_CHECK(GJGameState, m_unorderedMapEnterEffectInstanceVectors1, 0x430);
GEODE_MEMBER_CHECK(GJGameState, m_unorderedMapEnterEffectInstanceVectors2, 0x468);
GEODE_MEMBER_CHECK(GJGameState, m_unkVecInt1, 0x4a0);
GEODE_MEMBER_CHECK(GJGameState, m_unkVecInt2, 0x4b8);
GEODE_MEMBER_CHECK(GJGameState, m_enterEffectInstances1, 0x4d0);
GEODE_MEMBER_CHECK(GJGameState, m_enterEffectInstances2, 0x4e8);
GEODE_MEMBER_CHECK(GJGameState, m_enterEffectInstances3, 0x500);
GEODE_MEMBER_CHECK(GJGameState, m_enterEffectInstances4, 0x518);
GEODE_MEMBER_CHECK(GJGameState, m_enterEffectInstances5, 0x530);
GEODE_MEMBER_CHECK(GJGameState, m_unkUnorderedSet1, 0x548);
GEODE_MEMBER_CHECK(GJGameState, m_unkBool27, 0x580);
GEODE_MEMBER_CHECK(GJGameState, m_advanceFollowInstances, 0x588);
GEODE_MEMBER_CHECK(GJGameState, m_dynamicObjActions1, 0x5a0);
GEODE_MEMBER_CHECK(GJGameState, m_dynamicObjActions2, 0x5b8);
GEODE_MEMBER_CHECK(GJGameState, m_unkBool28, 0x5d0);
GEODE_MEMBER_CHECK(GJGameState, m_unkBool29, 0x5d1);
GEODE_MEMBER_CHECK(GJGameState, m_unkUint17, 0x5d4);
GEODE_MEMBER_CHECK(GJGameState, m_unkUMap8, 0x5d8);
GEODE_MEMBER_CHECK(GJGameState, m_proximityVolumeRelated, 0x610);
GEODE_MEMBER_CHECK(GJGameState, m_songChannelStates, 0x640);
GEODE_MEMBER_CHECK(GJGameState, m_songTriggerStateVectors, 0x678);
GEODE_MEMBER_CHECK(GJGameState, m_sfxTriggerStates, 0x6b0);
GEODE_MEMBER_CHECK(GJGameState, m_unkBool30, 0x6c8);
GEODE_MEMBER_CHECK(GJGameState, m_background, 0x6cc);
GEODE_MEMBER_CHECK(GJGameState, m_ground, 0x6d0);
GEODE_MEMBER_CHECK(GJGameState, m_middleground, 0x6d4);
GEODE_MEMBER_CHECK(GJGameState, m_unkBool31, 0x6d8);
GEODE_MEMBER_CHECK(GJGameState, m_points, 0x6dc);
GEODE_MEMBER_CHECK(GJGameState, m_unkBool32, 0x6e0);
GEODE_MEMBER_CHECK(GJGameState, m_pauseCounter, 0x6e4);
GEODE_MEMBER_CHECK(GJGameState, m_pauseBufferTimer, 0x6e8);

GEODE_SIZE_CHECK(GJShaderState, 0x2b8);
GEODE_MEMBER_CHECK(GJShaderState, m_someIntToValueTweenMap, 0x0);
GEODE_MEMBER_CHECK(GJShaderState, m_someIntToDoubleMap, 0x38);
GEODE_MEMBER_CHECK(GJShaderState, m_time, 0x70);
GEODE_MEMBER_CHECK(GJShaderState, m_prevTime, 0x78);
GEODE_MEMBER_CHECK(GJShaderState, m_startTime, 0x80);
GEODE_MEMBER_CHECK(GJShaderState, m_textureScaleX, 0x88);
GEODE_MEMBER_CHECK(GJShaderState, m_textureScaleY, 0x8c);
GEODE_MEMBER_CHECK(GJShaderState, m_blurRefColor, 0x90);
GEODE_MEMBER_CHECK(GJShaderState, m_blurIntensity, 0x94);
GEODE_MEMBER_CHECK(GJShaderState, m_blurUnk60, 0x98);
GEODE_MEMBER_CHECK(GJShaderState, m_blurOnlyEmpty, 0x9c);
GEODE_MEMBER_CHECK(GJShaderState, m_shockWaveUnk68, 0xa0);
GEODE_MEMBER_CHECK(GJShaderState, m_shockWaveUnk6c, 0xa4);
GEODE_MEMBER_CHECK(GJShaderState, m_shockWaveUnk70, 0xa8);
GEODE_MEMBER_CHECK(GJShaderState, m_shockWaveUnk74, 0xac);
GEODE_MEMBER_CHECK(GJShaderState, m_shockWaveUnk78, 0xb0);
GEODE_MEMBER_CHECK(GJShaderState, m_shockWaveUnk7c, 0xb4);
GEODE_MEMBER_CHECK(GJShaderState, m_shockWaveUnk80, 0xb8);
GEODE_MEMBER_CHECK(GJShaderState, m_shockWaveUnk84, 0xbc);
GEODE_MEMBER_CHECK(GJShaderState, m_shockWaveUnk88, 0xc0);
GEODE_MEMBER_CHECK(GJShaderState, m_shockWaveInvert, 0xc4);
GEODE_MEMBER_CHECK(GJShaderState, m_shockWaveUnk90, 0xc8);
GEODE_MEMBER_CHECK(GJShaderState, m_shockWaveUnk94, 0xcc);
GEODE_MEMBER_CHECK(GJShaderState, m_shockWaveUnk98, 0xd0);
GEODE_MEMBER_CHECK(GJShaderState, m_shockWaveUnk9c, 0xd4);
GEODE_MEMBER_CHECK(GJShaderState, m_shockWaveCenterMoving, 0xd5);
GEODE_MEMBER_CHECK(GJShaderState, m_shockWaveUnk9e, 0xd6);
GEODE_MEMBER_CHECK(GJShaderState, m_shockWaveUnka0, 0xd8);
GEODE_MEMBER_CHECK(GJShaderState, m_shockWaveUnka4, 0xdc);
GEODE_MEMBER_CHECK(GJShaderState, m_shockWaveCenterDirty, 0xe4);
GEODE_MEMBER_CHECK(GJShaderState, m_shockWaveCenter, 0xe8);
GEODE_MEMBER_CHECK(GJShaderState, m_shockLineUnkb8, 0xf0);
GEODE_MEMBER_CHECK(GJShaderState, m_shockLineUnkc0, 0xf8);
GEODE_MEMBER_CHECK(GJShaderState, m_shockLineUnkc4, 0xfc);
GEODE_MEMBER_CHECK(GJShaderState, m_shockLineUnkc8, 0x100);
GEODE_MEMBER_CHECK(GJShaderState, m_shockLineAxis, 0x104);
GEODE_MEMBER_CHECK(GJShaderState, m_shockLineDirection, 0x105);
GEODE_MEMBER_CHECK(GJShaderState, m_shockLineDual, 0x106);
GEODE_MEMBER_CHECK(GJShaderState, m_shockLineUnkcf, 0x107);
GEODE_MEMBER_CHECK(GJShaderState, m_shockLineUnkd0, 0x108);
GEODE_MEMBER_CHECK(GJShaderState, m_shockLineUnkd4, 0x10c);
GEODE_MEMBER_CHECK(GJShaderState, m_shockLineUnkd8, 0x110);
GEODE_MEMBER_CHECK(GJShaderState, m_shockLineUnkdc, 0x114);
GEODE_MEMBER_CHECK(GJShaderState, m_shockLineUnke0, 0x118);
GEODE_MEMBER_CHECK(GJShaderState, m_shockLineUnke4, 0x11c);
GEODE_MEMBER_CHECK(GJShaderState, m_shockLineUnke8, 0x120);
GEODE_MEMBER_CHECK(GJShaderState, m_shockLineUnkec, 0x124);
GEODE_MEMBER_CHECK(GJShaderState, m_shockLineCenterMoving, 0x125);
GEODE_MEMBER_CHECK(GJShaderState, m_shockLineUnkee, 0x126);
GEODE_MEMBER_CHECK(GJShaderState, m_shockLineUnkf0, 0x128);
GEODE_MEMBER_CHECK(GJShaderState, m_shockLineUnkf4, 0x12c);
GEODE_MEMBER_CHECK(GJShaderState, m_shockLineCenterDirty, 0x134);
GEODE_MEMBER_CHECK(GJShaderState, m_shockLineCenter, 0x138);
GEODE_MEMBER_CHECK(GJShaderState, m_glitchUnk108, 0x140);
GEODE_MEMBER_CHECK(GJShaderState, m_glitchUnk10c, 0x144);
GEODE_MEMBER_CHECK(GJShaderState, m_glitchUnk110, 0x148);
GEODE_MEMBER_CHECK(GJShaderState, m_glitchUnk114, 0x14c);
GEODE_MEMBER_CHECK(GJShaderState, m_glitchUnk118, 0x150);
GEODE_MEMBER_CHECK(GJShaderState, m_glitchUnk11c, 0x154);
GEODE_MEMBER_CHECK(GJShaderState, m_glitchUnk120, 0x158);
GEODE_MEMBER_CHECK(GJShaderState, m_chromaticUnk124, 0x15c);
GEODE_MEMBER_CHECK(GJShaderState, m_chromaticUnk128, 0x160);
GEODE_MEMBER_CHECK(GJShaderState, m_chromaticUnk12c, 0x164);
GEODE_MEMBER_CHECK(GJShaderState, m_cGUnk130, 0x168);
GEODE_MEMBER_CHECK(GJShaderState, m_cGUnk134, 0x16c);
GEODE_MEMBER_CHECK(GJShaderState, m_cGUnk138, 0x170);
GEODE_MEMBER_CHECK(GJShaderState, m_cGUnk13c, 0x174);
GEODE_MEMBER_CHECK(GJShaderState, m_cGUnk140, 0x178);
GEODE_MEMBER_CHECK(GJShaderState, m_cGUnk144, 0x17c);
GEODE_MEMBER_CHECK(GJShaderState, m_cGUnk148, 0x180);
GEODE_MEMBER_CHECK(GJShaderState, m_cGUnk14c, 0x184);
GEODE_MEMBER_CHECK(GJShaderState, m_cGUnk150, 0x188);
GEODE_MEMBER_CHECK(GJShaderState, m_cGUnk151, 0x189);
GEODE_MEMBER_CHECK(GJShaderState, m_cGUnk152, 0x18a);
GEODE_MEMBER_CHECK(GJShaderState, m_cGUnk153, 0x18b);
GEODE_MEMBER_CHECK(GJShaderState, m_pixelateTargetX, 0x18c);
GEODE_MEMBER_CHECK(GJShaderState, m_pixelateTargetY, 0x190);
GEODE_MEMBER_CHECK(GJShaderState, m_pixelateSnapGrid, 0x194);
GEODE_MEMBER_CHECK(GJShaderState, m_pixelatePixelating, 0x195);
GEODE_MEMBER_CHECK(GJShaderState, m_pixelateRelative, 0x196);
GEODE_MEMBER_CHECK(GJShaderState, m_pixelateHardEdges, 0x197);
GEODE_MEMBER_CHECK(GJShaderState, m_lensCircleUnk160, 0x198);
GEODE_MEMBER_CHECK(GJShaderState, m_lensCircleUnk164, 0x19c);
GEODE_MEMBER_CHECK(GJShaderState, m_lensCircleStrength, 0x1a0);
GEODE_MEMBER_CHECK(GJShaderState, m_lensCircleUnk16c, 0x1a4);
GEODE_MEMBER_CHECK(GJShaderState, m_lensCircleUnk170, 0x1a8);
GEODE_MEMBER_CHECK(GJShaderState, m_lensCircleUnk174, 0x1ac);
GEODE_MEMBER_CHECK(GJShaderState, m_lensCircleAdditive, 0x1ad);
GEODE_MEMBER_CHECK(GJShaderState, m_lensCircleTintR, 0x1ae);
GEODE_MEMBER_CHECK(GJShaderState, m_lensCircleTintG, 0x1af);
GEODE_MEMBER_CHECK(GJShaderState, m_lensCircleTintB, 0x1b0);
GEODE_MEMBER_CHECK(GJShaderState, m_lensCircleUnk17c, 0x1b4);
GEODE_MEMBER_CHECK(GJShaderState, m_lensCircleUnk184, 0x1bc);
GEODE_MEMBER_CHECK(GJShaderState, m_radialBlurUnk18c, 0x1c4);
GEODE_MEMBER_CHECK(GJShaderState, m_radialBlurUnk190, 0x1c8);
GEODE_MEMBER_CHECK(GJShaderState, m_radialBlurUnk194, 0x1cc);
GEODE_MEMBER_CHECK(GJShaderState, m_radialBlurUnk198, 0x1d0);
GEODE_MEMBER_CHECK(GJShaderState, m_radialBlurUnk19c, 0x1d4);
GEODE_MEMBER_CHECK(GJShaderState, m_radialBlurUnk1a4, 0x1dc);
GEODE_MEMBER_CHECK(GJShaderState, m_motionBlurUnk1ac, 0x1e4);
GEODE_MEMBER_CHECK(GJShaderState, m_motionBlurUnk1b0, 0x1e8);
GEODE_MEMBER_CHECK(GJShaderState, m_motionBlurSpeedX, 0x1ec);
GEODE_MEMBER_CHECK(GJShaderState, m_motionBlurSpeedY, 0x1f0);
GEODE_MEMBER_CHECK(GJShaderState, m_motionBlurUnk1bc, 0x1f4);
GEODE_MEMBER_CHECK(GJShaderState, m_motionBlurUnk1c0, 0x1f8);
GEODE_MEMBER_CHECK(GJShaderState, m_motionBlurUnk1c4, 0x1fc);
GEODE_MEMBER_CHECK(GJShaderState, m_motionBlurUnk1c8, 0x200);
GEODE_MEMBER_CHECK(GJShaderState, m_motionBlurDual, 0x204);
GEODE_MEMBER_CHECK(GJShaderState, m_motionBlurUnk1cd, 0x205);
GEODE_MEMBER_CHECK(GJShaderState, m_bulgeValue, 0x208);
GEODE_MEMBER_CHECK(GJShaderState, m_bulgeUnk1d4, 0x20c);
GEODE_MEMBER_CHECK(GJShaderState, m_bulgeUnk1d8, 0x210);
GEODE_MEMBER_CHECK(GJShaderState, m_bulgeRadius, 0x214);
GEODE_MEMBER_CHECK(GJShaderState, m_bulgeUnk1e0, 0x218);
GEODE_MEMBER_CHECK(GJShaderState, m_bulgeUnk1e8, 0x220);
GEODE_MEMBER_CHECK(GJShaderState, m_pinchUnk1f0, 0x228);
GEODE_MEMBER_CHECK(GJShaderState, m_pinchUnk1f4, 0x22c);
GEODE_MEMBER_CHECK(GJShaderState, m_pinchUnk1f8, 0x230);
GEODE_MEMBER_CHECK(GJShaderState, m_pinchUnk1f9, 0x231);
GEODE_MEMBER_CHECK(GJShaderState, m_pinchUnk1fc, 0x234);
GEODE_MEMBER_CHECK(GJShaderState, m_pinchUnk200, 0x238);
GEODE_MEMBER_CHECK(GJShaderState, m_pinchUnk204, 0x23c);
GEODE_MEMBER_CHECK(GJShaderState, m_pinchUnk20c, 0x244);
GEODE_MEMBER_CHECK(GJShaderState, m_pinchUnk214, 0x24c);
GEODE_MEMBER_CHECK(GJShaderState, m_pinchUnk218, 0x250);
GEODE_MEMBER_CHECK(GJShaderState, m_pinchUnk21c, 0x254);
GEODE_MEMBER_CHECK(GJShaderState, m_grayscaleValue, 0x25c);
GEODE_MEMBER_CHECK(GJShaderState, m_grayscaleUseLum, 0x260);
GEODE_MEMBER_CHECK(GJShaderState, m_grayscaleUnk22c, 0x264);
GEODE_MEMBER_CHECK(GJShaderState, m_grayscaleTint, 0x268);
GEODE_MEMBER_CHECK(GJShaderState, m_sepiaValue, 0x26c);
GEODE_MEMBER_CHECK(GJShaderState, m_invertColorEditRGB, 0x270);
GEODE_MEMBER_CHECK(GJShaderState, m_invertColorR, 0x274);
GEODE_MEMBER_CHECK(GJShaderState, m_invertColorG, 0x278);
GEODE_MEMBER_CHECK(GJShaderState, m_invertColorB, 0x27c);
GEODE_MEMBER_CHECK(GJShaderState, m_invertColorClampRGB, 0x280);
GEODE_MEMBER_CHECK(GJShaderState, m_hueShiftDegrees, 0x284);
GEODE_MEMBER_CHECK(GJShaderState, m_colorChangeCR, 0x288);
GEODE_MEMBER_CHECK(GJShaderState, m_colorChangeCG, 0x28c);
GEODE_MEMBER_CHECK(GJShaderState, m_colorChangeCB, 0x290);
GEODE_MEMBER_CHECK(GJShaderState, m_colorChangeBR, 0x294);
GEODE_MEMBER_CHECK(GJShaderState, m_colorChangeBG, 0x298);
GEODE_MEMBER_CHECK(GJShaderState, m_colorChangeBB, 0x29c);
GEODE_MEMBER_CHECK(GJShaderState, m_splitUnk268, 0x2a0);
GEODE_MEMBER_CHECK(GJShaderState, m_splitUnk26c, 0x2a4);
GEODE_MEMBER_CHECK(GJShaderState, m_splitUnk270, 0x2a8);
GEODE_MEMBER_CHECK(GJShaderState, m_minBlendingLayer, 0x2ac);
GEODE_MEMBER_CHECK(GJShaderState, m_maxBlendingLayer, 0x2b0);
GEODE_MEMBER_CHECK(GJShaderState, m_zLayerDirty, 0x2b4);
GEODE_MEMBER_CHECK(GJShaderState, m_somethingZLayerUnk27d, 0x2b5);
GEODE_MEMBER_CHECK(GJShaderState, m_usesShaders, 0x2b6);

GEODE_SIZE_CHECK(FMODSoundTween, 0x18);
GEODE_MEMBER_CHECK(FMODSoundTween, m_interval, 0x0);
GEODE_MEMBER_CHECK(FMODSoundTween, m_duration, 0x4);
GEODE_MEMBER_CHECK(FMODSoundTween, m_start, 0x8);
GEODE_MEMBER_CHECK(FMODSoundTween, m_end, 0xc);
GEODE_MEMBER_CHECK(FMODSoundTween, m_value, 0x10);
GEODE_MEMBER_CHECK(FMODSoundTween, m_finished, 0x14);

GEODE_SIZE_CHECK(FMODQueuedMusic, 0x50);
GEODE_MEMBER_CHECK(FMODQueuedMusic, m_filePath, 0x0);
GEODE_MEMBER_CHECK(FMODQueuedMusic, m_pitch, 0x8);
GEODE_MEMBER_CHECK(FMODQueuedMusic, m_unkFloat2, 0xc);
GEODE_MEMBER_CHECK(FMODQueuedMusic, m_volume, 0x10);
GEODE_MEMBER_CHECK(FMODQueuedMusic, m_start, 0x14);
GEODE_MEMBER_CHECK(FMODQueuedMusic, m_end, 0x18);
GEODE_MEMBER_CHECK(FMODQueuedMusic, m_fadeIn, 0x1c);
GEODE_MEMBER_CHECK(FMODQueuedMusic, m_fadeOut, 0x20);
GEODE_MEMBER_CHECK(FMODQueuedMusic, m_loop, 0x24);
GEODE_MEMBER_CHECK(FMODQueuedMusic, m_musicID, 0x28);
GEODE_MEMBER_CHECK(FMODQueuedMusic, m_stopMusic, 0x2c);
GEODE_MEMBER_CHECK(FMODQueuedMusic, m_channelID, 0x30);
GEODE_MEMBER_CHECK(FMODQueuedMusic, m_noPrepare, 0x34);
GEODE_MEMBER_CHECK(FMODQueuedMusic, m_queuedStatus, 0x38);
GEODE_MEMBER_CHECK(FMODQueuedMusic, m_sound, 0x40);
GEODE_MEMBER_CHECK(FMODQueuedMusic, m_dontReset, 0x48);

GEODE_SIZE_CHECK(SoundStateContainer, 0x68);
GEODE_MEMBER_CHECK(SoundStateContainer, m_fadePointCount, 0x0);
GEODE_MEMBER_CHECK(SoundStateContainer, m_fadePointVolumes, 0x4);
GEODE_MEMBER_CHECK(SoundStateContainer, m_fadePointOffsets, 0x18);
GEODE_MEMBER_CHECK(SoundStateContainer, m_currentOffset, 0x38);
GEODE_MEMBER_CHECK(SoundStateContainer, m_loopStartMs, 0x40);
GEODE_MEMBER_CHECK(SoundStateContainer, m_loopEndMs, 0x44);
GEODE_MEMBER_CHECK(SoundStateContainer, m_currentMs, 0x48);
GEODE_MEMBER_CHECK(SoundStateContainer, m_playStartOffset, 0x50);
GEODE_MEMBER_CHECK(SoundStateContainer, m_playEndOffset, 0x58);
GEODE_MEMBER_CHECK(SoundStateContainer, m_usePlayOffsets, 0x60);

GEODE_SIZE_CHECK(FMODSoundState, 0xa8);
GEODE_MEMBER_CHECK(FMODSoundState, m_filePath, 0x0);
GEODE_MEMBER_CHECK(FMODSoundState, m_speed, 0x8);
GEODE_MEMBER_CHECK(FMODSoundState, m_unkFloat1, 0xc);
GEODE_MEMBER_CHECK(FMODSoundState, m_volume, 0x10);
GEODE_MEMBER_CHECK(FMODSoundState, m_shouldLoop, 0x14);
GEODE_MEMBER_CHECK(FMODSoundState, m_channelID, 0x18);
GEODE_MEMBER_CHECK(FMODSoundState, m_soundStateContainer, 0x20);
GEODE_MEMBER_CHECK(FMODSoundState, m_uniqueID, 0x88);
GEODE_MEMBER_CHECK(FMODSoundState, m_sfxGroup, 0x8c);
GEODE_MEMBER_CHECK(FMODSoundState, m_pitch, 0x90);
GEODE_MEMBER_CHECK(FMODSoundState, m_fastFourierTransform, 0x94);
GEODE_MEMBER_CHECK(FMODSoundState, m_reverb, 0x95);
GEODE_MEMBER_CHECK(FMODSoundState, m_effectID, 0x98);
GEODE_MEMBER_CHECK(FMODSoundState, m_isMusic, 0x9c);
GEODE_MEMBER_CHECK(FMODSoundState, m_musicID, 0xa0);
GEODE_MEMBER_CHECK(FMODSoundState, m_unkBool2, 0xa4);

GEODE_SIZE_CHECK(FMODAudioState, 0x378);
GEODE_MEMBER_CHECK(FMODAudioState, m_interval, 0x0);
GEODE_MEMBER_CHECK(FMODAudioState, m_elapsed, 0x4);
GEODE_MEMBER_CHECK(FMODAudioState, m_tweensForEffectChannels, 0x8);
GEODE_MEMBER_CHECK(FMODAudioState, m_tweensForEffectGroups, 0x38);
GEODE_MEMBER_CHECK(FMODAudioState, m_tweensForMusicChannels, 0x68);
GEODE_MEMBER_CHECK(FMODAudioState, m_volumeForEffectChannels, 0x98);
GEODE_MEMBER_CHECK(FMODAudioState, m_volumeModForEffectChannels, 0xd0);
GEODE_MEMBER_CHECK(FMODAudioState, m_pitchForEffectChannels, 0x108);
GEODE_MEMBER_CHECK(FMODAudioState, m_volumeForEffectGroups, 0x140);
GEODE_MEMBER_CHECK(FMODAudioState, m_volumeModForEffectGroups, 0x178);
GEODE_MEMBER_CHECK(FMODAudioState, m_pitchForEffectGroups, 0x1b0);
GEODE_MEMBER_CHECK(FMODAudioState, m_volumeForMusicChannels, 0x1e8);
GEODE_MEMBER_CHECK(FMODAudioState, m_volumeModForMusicChannels, 0x220);
GEODE_MEMBER_CHECK(FMODAudioState, m_pitchForMusicChannels, 0x258);
GEODE_MEMBER_CHECK(FMODAudioState, m_intervalForEffects, 0x290);
GEODE_MEMBER_CHECK(FMODAudioState, m_queuedMusicForChannels1, 0x2c8);
GEODE_MEMBER_CHECK(FMODAudioState, m_queuedMusicForChannels2, 0x300);
GEODE_MEMBER_CHECK(FMODAudioState, m_soundStateForChannels, 0x338);
GEODE_MEMBER_CHECK(FMODAudioState, m_unkUint64_1, 0x370);

GEODE_SIZE_CHECK(PlayerCheckpoint, 0x1d0);
GEODE_MEMBER_CHECK(PlayerCheckpoint, m_position, 0x13c);
GEODE_MEMBER_CHECK(PlayerCheckpoint, m_lastPosition, 0x144);
GEODE_MEMBER_CHECK(PlayerCheckpoint, m_yVelocity, 0x14c);
GEODE_MEMBER_CHECK(PlayerCheckpoint, m_isUpsideDown, 0x150);
GEODE_MEMBER_CHECK(PlayerCheckpoint, m_isSideways, 0x151);
GEODE_MEMBER_CHECK(PlayerCheckpoint, m_isShip, 0x152);
GEODE_MEMBER_CHECK(PlayerCheckpoint, m_isBall, 0x153);
GEODE_MEMBER_CHECK(PlayerCheckpoint, m_isBird, 0x154);
GEODE_MEMBER_CHECK(PlayerCheckpoint, m_isSwing, 0x155);
GEODE_MEMBER_CHECK(PlayerCheckpoint, m_isDart, 0x156);
GEODE_MEMBER_CHECK(PlayerCheckpoint, m_isRobot, 0x157);
GEODE_MEMBER_CHECK(PlayerCheckpoint, m_isSpider, 0x158);
GEODE_MEMBER_CHECK(PlayerCheckpoint, m_isOnGround, 0x159);
GEODE_MEMBER_CHECK(PlayerCheckpoint, m_ghostType, 0x15c);
GEODE_MEMBER_CHECK(PlayerCheckpoint, m_miniMode, 0x160);
GEODE_MEMBER_CHECK(PlayerCheckpoint, m_speed, 0x164);
GEODE_MEMBER_CHECK(PlayerCheckpoint, m_hidden, 0x168);
GEODE_MEMBER_CHECK(PlayerCheckpoint, m_goingLeft, 0x169);
GEODE_MEMBER_CHECK(PlayerCheckpoint, m_reverseSpeed, 0x16c);
GEODE_MEMBER_CHECK(PlayerCheckpoint, m_dashing, 0x170);
GEODE_MEMBER_CHECK(PlayerCheckpoint, m_dashX, 0x174);
GEODE_MEMBER_CHECK(PlayerCheckpoint, m_dashY, 0x178);
GEODE_MEMBER_CHECK(PlayerCheckpoint, m_dashAngle, 0x17c);
GEODE_MEMBER_CHECK(PlayerCheckpoint, m_dashStartTime, 0x180);
GEODE_MEMBER_CHECK(PlayerCheckpoint, m_dashRingObject, 0x188);
GEODE_MEMBER_CHECK(PlayerCheckpoint, m_platformerCheckpoint, 0x190);
GEODE_MEMBER_CHECK(PlayerCheckpoint, m_lastFlipTime, 0x198);
GEODE_MEMBER_CHECK(PlayerCheckpoint, m_gravityMod, 0x1a0);
GEODE_MEMBER_CHECK(PlayerCheckpoint, m_decreaseBoostSlide, 0x1a4);
GEODE_MEMBER_CHECK(PlayerCheckpoint, m_followRelated, 0x1a8);
GEODE_MEMBER_CHECK(PlayerCheckpoint, m_playerFollowFloats, 0x1b0);
GEODE_MEMBER_CHECK(PlayerCheckpoint, m_followRelated2, 0x1c8);

GEODE_SIZE_CHECK(SavedObjectStateRef, 0x30);
GEODE_MEMBER_CHECK(SavedObjectStateRef, m_gameObject, 0x0);
GEODE_MEMBER_CHECK(SavedObjectStateRef, m_unkDouble1, 0x8);
GEODE_MEMBER_CHECK(SavedObjectStateRef, m_unkDouble2, 0x10);
GEODE_MEMBER_CHECK(SavedObjectStateRef, m_unkFloat1, 0x18);
GEODE_MEMBER_CHECK(SavedObjectStateRef, m_unkFloat2, 0x1c);
GEODE_MEMBER_CHECK(SavedObjectStateRef, m_addToCustomScaleX, 0x20);
GEODE_MEMBER_CHECK(SavedObjectStateRef, m_addToCustomScaleY, 0x24);
GEODE_MEMBER_CHECK(SavedObjectStateRef, m_unkFloat3, 0x28);
GEODE_MEMBER_CHECK(SavedObjectStateRef, m_unkFloat4, 0x2c);

GEODE_SIZE_CHECK(SavedActiveObjectState, 0x10);
GEODE_MEMBER_CHECK(SavedActiveObjectState, m_gameObject, 0x0);
GEODE_MEMBER_CHECK(SavedActiveObjectState, m_unkBool1, 0x8);
GEODE_MEMBER_CHECK(SavedActiveObjectState, m_unkBool2, 0x9);

GEODE_SIZE_CHECK(SavedSpecialObjectState, 0x10);
GEODE_MEMBER_CHECK(SavedSpecialObjectState, m_gameObject, 0x0);
GEODE_MEMBER_CHECK(SavedSpecialObjectState, m_animationID, 0x8);

GEODE_SIZE_CHECK(CAState, 0x44);
GEODE_MEMBER_CHECK(CAState, m_fromColor, 0x0);
GEODE_MEMBER_CHECK(CAState, m_toColor, 0x3);
GEODE_MEMBER_CHECK(CAState, m_color, 0x6);
GEODE_MEMBER_CHECK(CAState, m_paused, 0x9);
GEODE_MEMBER_CHECK(CAState, m_blending, 0xa);
GEODE_MEMBER_CHECK(CAState, m_copyOpacity, 0xb);
GEODE_MEMBER_CHECK(CAState, m_legacyHSV, 0xc);
GEODE_MEMBER_CHECK(CAState, m_playerColor, 0x10);
GEODE_MEMBER_CHECK(CAState, m_colorID, 0x14);
GEODE_MEMBER_CHECK(CAState, m_copyID, 0x18);
GEODE_MEMBER_CHECK(CAState, m_uniqueID, 0x1c);
GEODE_MEMBER_CHECK(CAState, m_duration, 0x20);
GEODE_MEMBER_CHECK(CAState, m_fromOpacity, 0x24);
GEODE_MEMBER_CHECK(CAState, m_toOpacity, 0x28);
GEODE_MEMBER_CHECK(CAState, m_deltaTime, 0x2c);
GEODE_MEMBER_CHECK(CAState, m_currentOpacity, 0x30);
GEODE_MEMBER_CHECK(CAState, m_copyHSV, 0x34);

GEODE_SIZE_CHECK(PulseEffectAction, 0x48);
GEODE_MEMBER_CHECK(PulseEffectAction, m_disabled, 0x0);
GEODE_MEMBER_CHECK(PulseEffectAction, m_fadeInTime, 0x4);
GEODE_MEMBER_CHECK(PulseEffectAction, m_holdTime, 0x8);
GEODE_MEMBER_CHECK(PulseEffectAction, m_fadeOutTime, 0xc);
GEODE_MEMBER_CHECK(PulseEffectAction, m_deltaTime, 0x10);
GEODE_MEMBER_CHECK(PulseEffectAction, m_targetGroupID, 0x14);
GEODE_MEMBER_CHECK(PulseEffectAction, m_currentValue, 0x18);
GEODE_MEMBER_CHECK(PulseEffectAction, m_color, 0x1c);
GEODE_MEMBER_CHECK(PulseEffectAction, m_pulseEffectType, 0x20);
GEODE_MEMBER_CHECK(PulseEffectAction, m_hsv, 0x24);
GEODE_MEMBER_CHECK(PulseEffectAction, m_colorIndex, 0x34);
GEODE_MEMBER_CHECK(PulseEffectAction, m_mainOnly, 0x38);
GEODE_MEMBER_CHECK(PulseEffectAction, m_detailOnly, 0x39);
GEODE_MEMBER_CHECK(PulseEffectAction, m_isDynamicHsv, 0x3a);
GEODE_MEMBER_CHECK(PulseEffectAction, m_triggerUniqueID, 0x3c);
GEODE_MEMBER_CHECK(PulseEffectAction, m_controlID, 0x40);
GEODE_MEMBER_CHECK(PulseEffectAction, m_startTime, 0x44);

GEODE_SIZE_CHECK(CountTriggerAction, 0x40);
GEODE_MEMBER_CHECK(CountTriggerAction, m_disabled, 0x0);
GEODE_MEMBER_CHECK(CountTriggerAction, m_previousCount, 0x4);
GEODE_MEMBER_CHECK(CountTriggerAction, m_targetCount, 0x8);
GEODE_MEMBER_CHECK(CountTriggerAction, m_targetGroupID, 0xc);
GEODE_MEMBER_CHECK(CountTriggerAction, m_activateGroup, 0x10);
GEODE_MEMBER_CHECK(CountTriggerAction, m_triggerUniqueID, 0x14);
GEODE_MEMBER_CHECK(CountTriggerAction, m_controlID, 0x18);
GEODE_MEMBER_CHECK(CountTriggerAction, m_itemID, 0x1c);
GEODE_MEMBER_CHECK(CountTriggerAction, m_multiActivate, 0x20);
GEODE_MEMBER_CHECK(CountTriggerAction, m_remapKeys, 0x28);

GEODE_SIZE_CHECK(OpacityEffectAction, 0x2c);
GEODE_MEMBER_CHECK(OpacityEffectAction, m_duration, 0x0);
GEODE_MEMBER_CHECK(OpacityEffectAction, m_fromValue, 0x4);
GEODE_MEMBER_CHECK(OpacityEffectAction, m_toValue, 0x8);
GEODE_MEMBER_CHECK(OpacityEffectAction, m_finished, 0xc);
GEODE_MEMBER_CHECK(OpacityEffectAction, m_disabled, 0xd);
GEODE_MEMBER_CHECK(OpacityEffectAction, m_deltaTime, 0x10);
GEODE_MEMBER_CHECK(OpacityEffectAction, m_targetGroupID, 0x14);
GEODE_MEMBER_CHECK(OpacityEffectAction, m_currentValue, 0x18);
GEODE_MEMBER_CHECK(OpacityEffectAction, m_triggerUniqueID, 0x1c);
GEODE_MEMBER_CHECK(OpacityEffectAction, m_controlID, 0x20);
GEODE_MEMBER_CHECK(OpacityEffectAction, m_deltaTimeRelated, 0x24);
GEODE_MEMBER_CHECK(OpacityEffectAction, m_durationRelated, 0x28);

GEODE_SIZE_CHECK(TouchToggleAction, 0x38);
GEODE_MEMBER_CHECK(TouchToggleAction, m_disabled, 0x0);
GEODE_MEMBER_CHECK(TouchToggleAction, m_targetGroupID, 0x4);
GEODE_MEMBER_CHECK(TouchToggleAction, m_holdMode, 0x8);
GEODE_MEMBER_CHECK(TouchToggleAction, m_touchTriggerType, 0xc);
GEODE_MEMBER_CHECK(TouchToggleAction, m_touchTriggerControl, 0x10);
GEODE_MEMBER_CHECK(TouchToggleAction, m_triggerUniqueID, 0x14);
GEODE_MEMBER_CHECK(TouchToggleAction, m_controlID, 0x18);
GEODE_MEMBER_CHECK(TouchToggleAction, m_dualMode, 0x1c);
GEODE_MEMBER_CHECK(TouchToggleAction, m_remapKeys, 0x20);

GEODE_SIZE_CHECK(CollisionTriggerAction, 0x38);
GEODE_MEMBER_CHECK(CollisionTriggerAction, m_disabled, 0x0);
GEODE_MEMBER_CHECK(CollisionTriggerAction, m_blockAID, 0x4);
GEODE_MEMBER_CHECK(CollisionTriggerAction, m_blockBID, 0x8);
GEODE_MEMBER_CHECK(CollisionTriggerAction, m_targetGroupID, 0xc);
GEODE_MEMBER_CHECK(CollisionTriggerAction, m_triggerOnExit, 0x10);
GEODE_MEMBER_CHECK(CollisionTriggerAction, m_activateGroup, 0x14);
GEODE_MEMBER_CHECK(CollisionTriggerAction, m_triggerUniqueID, 0x18);
GEODE_MEMBER_CHECK(CollisionTriggerAction, m_controlID, 0x1c);
GEODE_MEMBER_CHECK(CollisionTriggerAction, m_remapKeys, 0x20);

GEODE_SIZE_CHECK(ToggleTriggerAction, 0x30);
GEODE_MEMBER_CHECK(ToggleTriggerAction, m_disabled, 0x0);
GEODE_MEMBER_CHECK(ToggleTriggerAction, m_targetGroupID, 0x4);
GEODE_MEMBER_CHECK(ToggleTriggerAction, m_activateGroup, 0x8);
GEODE_MEMBER_CHECK(ToggleTriggerAction, m_triggerUniqueID, 0xc);
GEODE_MEMBER_CHECK(ToggleTriggerAction, m_controlID, 0x10);
GEODE_MEMBER_CHECK(ToggleTriggerAction, m_remapKeys, 0x18);

GEODE_SIZE_CHECK(SpawnTriggerAction, 0x48);
GEODE_MEMBER_CHECK(SpawnTriggerAction, m_finished, 0x0);
GEODE_MEMBER_CHECK(SpawnTriggerAction, m_disabled, 0x1);
GEODE_MEMBER_CHECK(SpawnTriggerAction, m_duration, 0x8);
GEODE_MEMBER_CHECK(SpawnTriggerAction, m_deltaTime, 0x10);
GEODE_MEMBER_CHECK(SpawnTriggerAction, m_targetGroupID, 0x18);
GEODE_MEMBER_CHECK(SpawnTriggerAction, m_triggerUniqueID, 0x1c);
GEODE_MEMBER_CHECK(SpawnTriggerAction, m_controlID, 0x20);
GEODE_MEMBER_CHECK(SpawnTriggerAction, m_spawnOrdered, 0x24);
GEODE_MEMBER_CHECK(SpawnTriggerAction, m_gameObject, 0x28);
GEODE_MEMBER_CHECK(SpawnTriggerAction, m_remapKeys, 0x30);\

GEODE_SIZE_CHECK(tk_spline, 0xa8);
GEODE_MEMBER_CHECK(tk_spline, m_x, 0x0);
GEODE_MEMBER_CHECK(tk_spline, m_y, 0x18);
GEODE_MEMBER_CHECK(tk_spline, m_b, 0x30);
GEODE_MEMBER_CHECK(tk_spline, m_c, 0x48);
GEODE_MEMBER_CHECK(tk_spline, m_d, 0x60);
GEODE_MEMBER_CHECK(tk_spline, m_c0, 0x78);
GEODE_MEMBER_CHECK(tk_spline, m_type, 0x80);
GEODE_MEMBER_CHECK(tk_spline, m_left, 0x84);
GEODE_MEMBER_CHECK(tk_spline, m_right, 0x88);
GEODE_MEMBER_CHECK(tk_spline, m_leftValue, 0x90);
GEODE_MEMBER_CHECK(tk_spline, m_rightValue, 0x98);
GEODE_MEMBER_CHECK(tk_spline, m_madeMonotonic, 0xa0);

GEODE_SIZE_CHECK(KeyframeObject, 0x1c0);
GEODE_MEMBER_CHECK(KeyframeObject, m_unk000, 0x0);
GEODE_MEMBER_CHECK(KeyframeObject, m_unk008, 0x8);
GEODE_MEMBER_CHECK(KeyframeObject, m_unk00c, 0xc);
GEODE_MEMBER_CHECK(KeyframeObject, m_unk010, 0x10);
GEODE_MEMBER_CHECK(KeyframeObject, m_unk014, 0x14);
GEODE_MEMBER_CHECK(KeyframeObject, m_unk018, 0x18);
GEODE_MEMBER_CHECK(KeyframeObject, m_unk01c, 0x1c);
GEODE_MEMBER_CHECK(KeyframeObject, m_unk01d, 0x1d);
GEODE_MEMBER_CHECK(KeyframeObject, m_unk01e, 0x1e);
GEODE_MEMBER_CHECK(KeyframeObject, m_spline1, 0x20);
GEODE_MEMBER_CHECK(KeyframeObject, m_spline2, 0xc8);
GEODE_MEMBER_CHECK(KeyframeObject, m_unk170, 0x170);
GEODE_MEMBER_CHECK(KeyframeObject, m_unk178, 0x178);
GEODE_MEMBER_CHECK(KeyframeObject, m_unk180, 0x180);
GEODE_MEMBER_CHECK(KeyframeObject, m_unk184, 0x184);
GEODE_MEMBER_CHECK(KeyframeObject, m_unk188, 0x188);
GEODE_MEMBER_CHECK(KeyframeObject, m_unk190, 0x190);
GEODE_MEMBER_CHECK(KeyframeObject, m_unk198, 0x198);
GEODE_MEMBER_CHECK(KeyframeObject, m_unk1a0, 0x1a0);
GEODE_MEMBER_CHECK(KeyframeObject, m_unk1a8, 0x1a8);
GEODE_MEMBER_CHECK(KeyframeObject, m_unk1b0, 0x1b0);
GEODE_MEMBER_CHECK(KeyframeObject, m_unk1b8, 0x1b8);

GEODE_SIZE_CHECK(GroupCommandObject2, 0x208);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_groupCommandUniqueID, 0x0);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_moveOffset, 0x4);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_easingType, 0xc);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_easingRate, 0x10);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_duration, 0x18);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_deltaTime, 0x20);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_targetGroupID, 0x28);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_centerGroupID, 0x2c);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_currentXOffset, 0x30);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_currentYOffset, 0x38);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_deltaX, 0x40);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_deltaY, 0x48);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_oldDeltaX, 0x50);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_oldDeltaY, 0x58);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_lockedCurrentXOffset, 0x60);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_lockedCurrentYOffset, 0x68);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_finished, 0x70);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_disabled, 0x71);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_finishRelated, 0x72);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_lockToPlayerX, 0x73);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_lockToPlayerY, 0x74);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_lockToCameraX, 0x75);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_lockToCameraY, 0x76);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_lockedInX, 0x77);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_lockedInY, 0x78);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_moveModX, 0x80);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_moveModY, 0x88);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_currentRotateOrTransformValue, 0x90);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_currentRotateOrTransformDelta, 0x98);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_someInterpValue1RelatedOne, 0xa0);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_someInterpValue2RelatedOne, 0xa8);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_rotationOffset, 0xb0);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_lockObjectRotation, 0xb8);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_targetPlayer, 0xbc);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_followXMod, 0xc0);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_followYMod, 0xc8);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_commandType, 0xd0);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_someInterpValue1, 0xd8);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_someInterpValue2, 0xe0);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_keyframeRelated, 0xe8);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_targetScaleX, 0xf0);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_targetScaleY, 0xf8);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_transformTriggerProperty450, 0x100);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_transformTriggerProperty451, 0x108);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_someInterpValue1RelatedZero, 0x110);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_someInterpValue2RelatedZero, 0x118);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_onlyMove, 0x120);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_transformRelatedFalse, 0x121);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_relativeRotation, 0x122);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_someInterpValue1Related, 0x128);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_someInterpValue2Related, 0x130);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_followYSpeed, 0x138);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_followYDelay, 0x140);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_followYOffset, 0x148);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_followYMaxSpeed, 0x150);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_triggerUniqueID, 0x158);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_controlID, 0x15c);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_deltaX_3, 0x160);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_deltaY_3, 0x168);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_oldDeltaX_3, 0x170);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_oldDeltaY_3, 0x178);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_Delta_3_Related, 0x180);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_unkDoubleMaybeUnused, 0x188);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_actionType1, 0x190);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_actionType2, 0x194);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_actionValue1, 0x198);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_actionValue2, 0x1a0);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_someInterpValue1RelatedFalse, 0x1a8);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_deltaTimeInFloat, 0x1ac);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_alreadyUpdated, 0x1b0);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_doUpdate, 0x1b1);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_keyframes, 0x1b8);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_splineRelated, 0x1d0);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_gameObject, 0x1d8);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_gameObjectRotation, 0x1e0);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_remapKeys, 0x1e8);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_someInterpValue2RelatedTrue, 0x200);
GEODE_MEMBER_CHECK(GroupCommandObject2, m_unkFloat204, 0x204);

GEODE_SIZE_CHECK(TimerItem, 0x58);
GEODE_MEMBER_CHECK(TimerItem, m_itemID, 0x0);
GEODE_MEMBER_CHECK(TimerItem, m_time, 0x8);
GEODE_MEMBER_CHECK(TimerItem, m_paused, 0x10);
GEODE_MEMBER_CHECK(TimerItem, m_timeMod, 0x14);
GEODE_MEMBER_CHECK(TimerItem, m_ignoreTimeWarp, 0x18);
GEODE_MEMBER_CHECK(TimerItem, m_targetTime, 0x20);
GEODE_MEMBER_CHECK(TimerItem, m_stopTimeEnabled, 0x28);
GEODE_MEMBER_CHECK(TimerItem, m_targetGroupID, 0x2c);
GEODE_MEMBER_CHECK(TimerItem, m_triggerUniqueID, 0x30);
GEODE_MEMBER_CHECK(TimerItem, m_controlID, 0x34);
GEODE_MEMBER_CHECK(TimerItem, m_remapKeys, 0x38);
GEODE_MEMBER_CHECK(TimerItem, m_disabled, 0x50);

GEODE_SIZE_CHECK(TimerTriggerAction, 0x38);
GEODE_MEMBER_CHECK(TimerTriggerAction, m_disabled, 0x0);
GEODE_MEMBER_CHECK(TimerTriggerAction, m_time, 0x4);
GEODE_MEMBER_CHECK(TimerTriggerAction, m_targetTime, 0x8);
GEODE_MEMBER_CHECK(TimerTriggerAction, m_targetGroupID, 0xc);
GEODE_MEMBER_CHECK(TimerTriggerAction, m_triggerUniqueID, 0x10);
GEODE_MEMBER_CHECK(TimerTriggerAction, m_controlID, 0x14);
GEODE_MEMBER_CHECK(TimerTriggerAction, m_itemID, 0x18);
GEODE_MEMBER_CHECK(TimerTriggerAction, m_multiActivate, 0x1c);
GEODE_MEMBER_CHECK(TimerTriggerAction, m_remapKeys, 0x20);

GEODE_SIZE_CHECK(EffectManagerState, 0x308);
GEODE_MEMBER_CHECK(EffectManagerState, m_unkVecCAState, 0x0);
GEODE_MEMBER_CHECK(EffectManagerState, m_unkVecPulseEffectAction, 0x18);
GEODE_MEMBER_CHECK(EffectManagerState, m_unorderedMapInt_vectorPulseEffectAction, 0x30);
GEODE_MEMBER_CHECK(EffectManagerState, m_unorderedMapInt_vectorCountTriggerAction, 0x68);
GEODE_MEMBER_CHECK(EffectManagerState, m_unorderedSet_int1, 0xa0);
GEODE_MEMBER_CHECK(EffectManagerState, m_mapInt_Int, 0xd8);
GEODE_MEMBER_CHECK(EffectManagerState, m_unorderedMapInt_OpacityEffectAction, 0x108);
GEODE_MEMBER_CHECK(EffectManagerState, m_vectorTouchToggleAction, 0x140);
GEODE_MEMBER_CHECK(EffectManagerState, m_vectorCollisionTriggerAction, 0x158);
GEODE_MEMBER_CHECK(EffectManagerState, m_vectorToggleTriggerAction, 0x170);
GEODE_MEMBER_CHECK(EffectManagerState, m_vectorSpawnTriggerAction, 0x188);
GEODE_MEMBER_CHECK(EffectManagerState, m_itemCountMap, 0x1a0);
GEODE_MEMBER_CHECK(EffectManagerState, m_unorderedMapInt_bool, 0x1d8);
GEODE_MEMBER_CHECK(EffectManagerState, m_vectorGroupCommandObject2, 0x210);
GEODE_MEMBER_CHECK(EffectManagerState, m_unorderedMapInt_pair_double_double, 0x228);
GEODE_MEMBER_CHECK(EffectManagerState, m_unorderedSet_int2, 0x260);
GEODE_MEMBER_CHECK(EffectManagerState, m_timerItemMap, 0x298);
GEODE_MEMBER_CHECK(EffectManagerState, m_unorderedMapInt_vectorTimerTriggerAction, 0x2d0);

GEODE_SIZE_CHECK(SequenceTriggerState, 0x70);
GEODE_MEMBER_CHECK(SequenceTriggerState, m_unkUnorderedMap1, 0x0);
GEODE_MEMBER_CHECK(SequenceTriggerState, m_unkUnorderedMap2, 0x38);

GEODE_SIZE_CHECK(CheckpointObject, 0x1230);
GEODE_MEMBER_CHECK(CheckpointObject, m_gameState, 0x140);
GEODE_MEMBER_CHECK(CheckpointObject, m_shaderState, 0x830);
GEODE_MEMBER_CHECK(CheckpointObject, m_audioState, 0xae8);
GEODE_MEMBER_CHECK(CheckpointObject, m_physicalCheckpointObject, 0xe60);
GEODE_MEMBER_CHECK(CheckpointObject, m_player1Checkpoint, 0xe68);
GEODE_MEMBER_CHECK(CheckpointObject, m_player2Checkpoint, 0xe70);
GEODE_MEMBER_CHECK(CheckpointObject, m_unke78, 0xe78);
GEODE_MEMBER_CHECK(CheckpointObject, m_unke7c, 0xe7c);
GEODE_MEMBER_CHECK(CheckpointObject, m_unke80, 0xe80);
GEODE_MEMBER_CHECK(CheckpointObject, m_ground2Invisible, 0xe84);
GEODE_MEMBER_CHECK(CheckpointObject, m_streakBlend, 0xe85);
GEODE_MEMBER_CHECK(CheckpointObject, m_uniqueID, 0xe88);
GEODE_MEMBER_CHECK(CheckpointObject, m_respawnID, 0xe8c);
GEODE_MEMBER_CHECK(CheckpointObject, m_vectorSavedObjectStateRef, 0xe90);
GEODE_MEMBER_CHECK(CheckpointObject, m_vectorActiveSaveObjectState, 0xea8);
GEODE_MEMBER_CHECK(CheckpointObject, m_vectorSpecialSaveObjectState, 0xec0);
GEODE_MEMBER_CHECK(CheckpointObject, m_effectManagerState, 0xed8);
GEODE_MEMBER_CHECK(CheckpointObject, m_gradientTriggerObjectArray, 0x11e0);
GEODE_MEMBER_CHECK(CheckpointObject, m_unk11e8, 0x11e8);
GEODE_MEMBER_CHECK(CheckpointObject, m_sequenceTriggerStateUnorderedMap, 0x11f0);
GEODE_MEMBER_CHECK(CheckpointObject, m_commandIndex, 0x1228);

#endif
