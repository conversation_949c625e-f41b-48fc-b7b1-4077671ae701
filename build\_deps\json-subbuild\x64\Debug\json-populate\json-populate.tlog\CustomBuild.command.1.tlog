^C:\USERS\<USER>\DOWNLOADS\CERDOOINK-MOD\BUILD\_DEPS\JSON-SUBBUILD\CMAKEFILES\A29CB58BD55BAF4FD05E81B8FA49B7E2\JSON-POPULATE-MKDIR.RULE
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -Dcfgdir=/Debug -P C:/Users/<USER>/Downloads/cerdoOINK-MOD/build/_deps/json-subbuild/json-populate-prefix/tmp/json-populate-mkdirs.cmake
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch C:/Users/<USER>/Downloads/cerdoOINK-MOD/build/_deps/json-subbuild/json-populate-prefix/src/json-populate-stamp/Debug/json-populate-mkdir
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\DOWNLOADS\CERDOOINK-MOD\BUILD\_DEPS\JSON-SUBBUILD\CMAKEFILES\A29CB58BD55BAF4FD05E81B8FA49B7E2\JSON-POPULATE-DOWNLOAD.RULE
setlocal
cd C:\Users\<USER>\Downloads\cerdoOINK-MOD\build\_deps
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_MESSAGE_LOG_LEVEL=VERBOSE -P C:/Users/<USER>/Downloads/cerdoOINK-MOD/build/_deps/json-subbuild/json-populate-prefix/tmp/json-populate-gitclone.cmake
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch C:/Users/<USER>/Downloads/cerdoOINK-MOD/build/_deps/json-subbuild/json-populate-prefix/src/json-populate-stamp/Debug/json-populate-download
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\DOWNLOADS\CERDOOINK-MOD\BUILD\_DEPS\JSON-SUBBUILD\CMAKEFILES\A29CB58BD55BAF4FD05E81B8FA49B7E2\JSON-POPULATE-UPDATE.RULE
setlocal
cd C:\Users\<USER>\Downloads\cerdoOINK-MOD\build\_deps\json-src
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -Dcan_fetch=YES -DCMAKE_MESSAGE_LOG_LEVEL=VERBOSE -P C:/Users/<USER>/Downloads/cerdoOINK-MOD/build/_deps/json-subbuild/json-populate-prefix/tmp/json-populate-gitupdate.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\DOWNLOADS\CERDOOINK-MOD\BUILD\_DEPS\JSON-SUBBUILD\CMAKEFILES\A29CB58BD55BAF4FD05E81B8FA49B7E2\JSON-POPULATE-PATCH.RULE
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch C:/Users/<USER>/Downloads/cerdoOINK-MOD/build/_deps/json-subbuild/json-populate-prefix/src/json-populate-stamp/Debug/json-populate-patch
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\DOWNLOADS\CERDOOINK-MOD\BUILD\_DEPS\JSON-SUBBUILD\CMAKEFILES\A29CB58BD55BAF4FD05E81B8FA49B7E2\JSON-POPULATE-CONFIGURE.RULE
setlocal
cd C:\Users\<USER>\Downloads\cerdoOINK-MOD\build\_deps\json-build
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch C:/Users/<USER>/Downloads/cerdoOINK-MOD/build/_deps/json-subbuild/json-populate-prefix/src/json-populate-stamp/Debug/json-populate-configure
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\DOWNLOADS\CERDOOINK-MOD\BUILD\_DEPS\JSON-SUBBUILD\CMAKEFILES\A29CB58BD55BAF4FD05E81B8FA49B7E2\JSON-POPULATE-BUILD.RULE
setlocal
cd C:\Users\<USER>\Downloads\cerdoOINK-MOD\build\_deps\json-build
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch C:/Users/<USER>/Downloads/cerdoOINK-MOD/build/_deps/json-subbuild/json-populate-prefix/src/json-populate-stamp/Debug/json-populate-build
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\DOWNLOADS\CERDOOINK-MOD\BUILD\_DEPS\JSON-SUBBUILD\CMAKEFILES\A29CB58BD55BAF4FD05E81B8FA49B7E2\JSON-POPULATE-INSTALL.RULE
setlocal
cd C:\Users\<USER>\Downloads\cerdoOINK-MOD\build\_deps\json-build
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch C:/Users/<USER>/Downloads/cerdoOINK-MOD/build/_deps/json-subbuild/json-populate-prefix/src/json-populate-stamp/Debug/json-populate-install
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\DOWNLOADS\CERDOOINK-MOD\BUILD\_DEPS\JSON-SUBBUILD\CMAKEFILES\A29CB58BD55BAF4FD05E81B8FA49B7E2\JSON-POPULATE-TEST.RULE
setlocal
cd C:\Users\<USER>\Downloads\cerdoOINK-MOD\build\_deps\json-build
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch C:/Users/<USER>/Downloads/cerdoOINK-MOD/build/_deps/json-subbuild/json-populate-prefix/src/json-populate-stamp/Debug/json-populate-test
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\DOWNLOADS\CERDOOINK-MOD\BUILD\_DEPS\JSON-SUBBUILD\CMAKEFILES\2BC83DFD7FAD986E4FE53B07BA4A993A\JSON-POPULATE-COMPLETE.RULE
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory C:/Users/<USER>/Downloads/cerdoOINK-MOD/build/_deps/json-subbuild/CMakeFiles/Debug
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch C:/Users/<USER>/Downloads/cerdoOINK-MOD/build/_deps/json-subbuild/CMakeFiles/Debug/json-populate-complete
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch C:/Users/<USER>/Downloads/cerdoOINK-MOD/build/_deps/json-subbuild/json-populate-prefix/src/json-populate-stamp/Debug/json-populate-done
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\DOWNLOADS\CERDOOINK-MOD\BUILD\_DEPS\JSON-SUBBUILD\CMAKEFILES\4B1924BFB67F83052C72FC668DC18CDC\JSON-POPULATE.RULE
setlocal
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\DOWNLOADS\CERDOOINK-MOD\BUILD\_DEPS\JSON-SUBBUILD\CMAKELISTS.TXT
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/Users/<USER>/Downloads/cerdoOINK-MOD/build/_deps/json-subbuild -BC:/Users/<USER>/Downloads/cerdoOINK-MOD/build/_deps/json-subbuild --check-stamp-file C:/Users/<USER>/Downloads/cerdoOINK-MOD/build/_deps/json-subbuild/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
