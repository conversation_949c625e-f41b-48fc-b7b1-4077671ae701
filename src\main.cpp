#include <Geode/Geode.hpp>
#include <Geode/modify/PlayerObject.hpp>
#include <Geode/modify/CCKeyboardDispatcher.hpp>

using namespace geode::prelude;

// Variable global para controlar el estado del cubo estático
bool staticCubeEnabled = false;

// Modificación del PlayerObject para controlar la rotación
class $modify(StaticPlayerObject, PlayerObject) {
    void update(float dt) {
        PlayerObject::update(dt);
        
        // Si el modo cubo estático está activado, forzar rotación a 0
        if (staticCubeEnabled) {
            // Usar CCNode::setRotation directamente
            CCNode::setRotation(0.0f);
        }
    }
};

// Modificación del dispatcher de teclado para detectar la tecla Z
class $modify(StaticKeyboardDispatcher, CCKeyboardDispatcher) {
    bool dispatchKeyboardMSG(enumKeyCodes key, bool isKeyDown, bool isKeyRepeat) {
        
        // Interceptar tecla Z para toggle del cubo estático
        if (key == KEY_Z && isKeyDown && !isKeyRepeat) {
            staticCubeEnabled = !staticCubeEnabled;
            
            // Mostrar notificación del estado
            if (auto* playLayer = PlayLayer::get()) {
                std::string message = staticCubeEnabled ? "Cubo Estático: ON" : "Cubo Estático: OFF";
                
                // Crear una notificación simple
                auto* notification = CCLabelBMFont::create(message.c_str(), "bigFont.fnt");
                notification->setScale(0.5f);
                notification->setOpacity(255);
                
                // Posicionar en el centro de la pantalla
                CCSize winSize = CCDirector::sharedDirector()->getWinSize();
                notification->setPosition(winSize.width / 2, winSize.height / 2 + 50);
                
                playLayer->addChild(notification, 1000);
                
                // Crear animación de desvanecimiento
                auto* fadeOut = CCFadeOut::create(2.0f);
                auto* remove = CCCallFunc::create(notification, callfunc_selector(CCNode::removeFromParent));
                auto* sequence = CCSequence::create(fadeOut, remove, nullptr);
                
                notification->runAction(sequence);
            }
        }
        
        return CCKeyboardDispatcher::dispatchKeyboardMSG(key, isKeyDown, isKeyRepeat);
    }
};

// Función de inicialización del mod
$execute {
    log::info("cerdoOINK MOD cargado correctamente!");
    log::info("Presiona Z para activar/desactivar el cubo estático");
};
