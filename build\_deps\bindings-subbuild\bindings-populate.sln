﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "CMakePredefinedTargets", "CMakePredefinedTargets", "{F582F16B-8D30-36AA-AB33-C9A88646F7A5}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "ExternalProjectTargets", "ExternalProjectTargets", "{BA234662-334B-3A04-9E75-EF5623626A4D}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "bindings-populate", "ExternalProjectTargets\bindings-populate", "{1A6015DE-8FE4-383C-B945-130626D4FF5D}"
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{895AD759-F94D-3EDE-A8C3-59B774D8EA49}"
	ProjectSection(ProjectDependencies) = postProject
		{46D0D1B8-B753-3D25-B718-849D6C431EDA} = {46D0D1B8-B753-3D25-B718-849D6C431EDA}
		{A87B2501-384C-36CB-8425-175E5B6058A4} = {A87B2501-384C-36CB-8425-175E5B6058A4}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{46D0D1B8-B753-3D25-B718-849D6C431EDA}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "bindings-populate", "bindings-populate.vcxproj", "{A87B2501-384C-36CB-8425-175E5B6058A4}"
	ProjectSection(ProjectDependencies) = postProject
		{46D0D1B8-B753-3D25-B718-849D6C431EDA} = {46D0D1B8-B753-3D25-B718-849D6C431EDA}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{895AD759-F94D-3EDE-A8C3-59B774D8EA49}.Debug|x64.ActiveCfg = Debug|x64
		{46D0D1B8-B753-3D25-B718-849D6C431EDA}.Debug|x64.ActiveCfg = Debug|x64
		{46D0D1B8-B753-3D25-B718-849D6C431EDA}.Debug|x64.Build.0 = Debug|x64
		{A87B2501-384C-36CB-8425-175E5B6058A4}.Debug|x64.ActiveCfg = Debug|x64
		{A87B2501-384C-36CB-8425-175E5B6058A4}.Debug|x64.Build.0 = Debug|x64
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{895AD759-F94D-3EDE-A8C3-59B774D8EA49} = {F582F16B-8D30-36AA-AB33-C9A88646F7A5}
		{46D0D1B8-B753-3D25-B718-849D6C431EDA} = {F582F16B-8D30-36AA-AB33-C9A88646F7A5}
		{1A6015DE-8FE4-383C-B945-130626D4FF5D} = {BA234662-334B-3A04-9E75-EF5623626A4D}
		{A87B2501-384C-36CB-8425-175E5B6058A4} = {1A6015DE-8FE4-383C-B945-130626D4FF5D}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {80BABCC7-AA56-352C-8AAF-CFD0BD10050F}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
