@echo off
echo ========================================
echo    Compilando cerdoOINK MOD
echo ========================================
echo.

REM Verificar que existe el SDK de Geode
if not defined GEODE_SDK (
    echo ERROR: Variable GEODE_SDK no está definida
    echo Por favor define la variable de entorno GEODE_SDK
    echo Ejemplo: set GEODE_SDK=C:\path\to\geode\sdk
    pause
    exit /b 1
)

echo SDK de Geode encontrado en: %GEODE_SDK%
echo.

REM Crear directorio build si no existe
if not exist "build" (
    echo Creando directorio build...
    mkdir build
)

echo Compilando mod con Geode...
geode build

if errorlevel 1 (
    echo ERROR: Falló la compilación
    pause
    exit /b 1
)

echo.
echo ========================================
echo    ¡Compilación exitosa!
echo ========================================
echo.

REM Verificar que el archivo .geode se creó
if exist "build\cerdooink.staticcube.geode" (
    echo Archivo compilado: build\cerdooink.staticcube.geode
    echo.
    echo Para instalar el mod:
    echo 1. Copia el archivo .geode a tu carpeta de mods de Geode
    echo 2. Reinicia Geometry Dash
    echo 3. ¡Presiona Z en cualquier nivel para usar el cubo estático!
) else (
    echo ADVERTENCIA: No se encontró el archivo .geode compilado
    echo Revisa los mensajes de error arriba
)

echo.
pause
