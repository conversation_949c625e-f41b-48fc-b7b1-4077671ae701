---
IndentWidth: 4

AlignAfterOpenBracket: BlockIndent
AlignEscapedNewlines: Left
AlignConsecutiveAssignments: false
AlignOperands: DontAlign
AlignTrailingComments: false

AllowAllArgumentsOnNextLine: true
AllowAllParametersOfDeclarationOnNextLine: false

AllowShortBlocksOnASingleLine: Empty
AllowShortFunctionsOnASingleLine: Empty
AllowShortLambdasOnASingleLine: Empty

AllowShortCaseLabelsOnASingleLine: true
AllowShortEnumsOnASingleLine: false

AllowShortIfStatementsOnASingleLine: AllIfsAndElse
AlwaysBreakAfterReturnType: None

AlwaysBreakBeforeMultilineStrings: true
AlwaysBreakTemplateDeclarations: Yes

BinPackArguments: false
BinPackParameters: true

BraceWrapping:
    AfterCaseLabel: false
    AfterClass: false
    AfterControlStatement: Never
    AfterEnum: false
    AfterFunction: false
    AfterNamespace: false
    AfterStruct: false
    AfterUnion: false
    AfterExternBlock: false

    BeforeCatch: true
    BeforeElse: true

    BeforeLambdaBody: false
    BeforeWhile: false
    IndentBraces: false
    SplitEmptyFunction: false
    SplitEmptyRecord: false
    SplitEmptyNamespace: false

BreakBeforeBinaryOperators: None
BreakBeforeBraces: Custom
BreakBeforeConceptDeclarations: true
BreakBeforeTernaryOperators: false
BreakConstructorInitializers: AfterColon
BreakInheritanceList: AfterColon
BreakStringLiterals: true
ColumnLimit: 100
CompactNamespaces: false
ConstructorInitializerIndentWidth: 4
ContinuationIndentWidth: 4
Cpp11BracedListStyle: true

DeriveLineEnding: false
UseCRLF: false
UseTab: Never

PointerAlignment: Left
ReferenceAlignment: Pointer

EmptyLineBeforeAccessModifier: Always
FixNamespaceComments: false

IncludeBlocks: Regroup
IncludeCategories:
-   Regex: '^".*"'
    Priority: 1

-   Regex: '^<.*>'
    Priority: 2

IndentAccessModifiers: false
AccessModifierOffset: -4
IndentCaseBlocks: false
IndentCaseLabels: true
IndentExternBlock: Indent
IndentGotoLabels: true
IndentPPDirectives: BeforeHash
IndentRequiresClause: true
IndentWrappedFunctionNames: false
# InsertBraces: true
InsertTrailingCommas: None

KeepEmptyLinesAtTheStartOfBlocks: false
MaxEmptyLinesToKeep: 1
LambdaBodyIndentation: Signature
NamespaceIndentation: All

PPIndentWidth: -1

PenaltyBreakAssignment: 2
PenaltyBreakBeforeFirstCallParameter: 19
PenaltyBreakComment: 300
PenaltyBreakFirstLessLess: 120
PenaltyBreakString: 1000
PenaltyBreakOpenParenthesis: 0
PenaltyBreakTemplateDeclaration: 10
PenaltyExcessCharacter: 10
PenaltyReturnTypeOnItsOwnLine: 1000000
PenaltyIndentedWhitespace: 0

QualifierAlignment: Right

RequiresClausePosition: OwnLine
ReflowComments: true
SeparateDefinitionBlocks: Always

SortIncludes: CaseSensitive
SortUsingDeclarations: true

SpaceAfterCStyleCast: false
SpaceAfterLogicalNot: false
SpaceAfterTemplateKeyword: true
SpaceAroundPointerQualifiers: Default
SpaceBeforeAssignmentOperators: true
SpaceBeforeCaseColon: false
SpaceBeforeCpp11BracedList: false
SpaceBeforeCtorInitializerColon: true
SpaceBeforeInheritanceColon: true
SpaceBeforeParens: ControlStatements
SpaceBeforeRangeBasedForLoopColon: true
SpaceBeforeSquareBrackets: false
SpaceInEmptyBlock: false
SpaceInEmptyParentheses: false
SpacesBeforeTrailingComments: 1
SpacesInAngles: Never
SpacesInCStyleCastParentheses: false
SpacesInConditionalStatement: false
SpacesInLineCommentPrefix:
    Minimum: 1
    Maximum: -1
SpacesInParentheses: false
SpacesInSquareBrackets: false
Standard: c++20
